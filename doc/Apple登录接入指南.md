# Apple 登录接入指南

本文档详细介绍了如何在 CalmWatch 应用中集成 Apple 登录功能。

## 前置条件

- Xcode 11.0 或更高版本
- iOS 13.0 或更高版本的设备或模拟器
- Apple Developer 账户
- 在 Apple Developer 门户中配置的 App ID 和 Associated Domains

## 1. 配置 Apple 登录

### 1.1 配置 App ID

1. 登录 [Apple Developer 网站](https://developer.apple.com/)
2. 进入 "Certificates, Identifiers & Profiles"
3. 选择 "Identifiers" 并找到您的应用 ID
4. 确保已启用 "Sign in with Apple" 功能
5. 保存更改

### 1.2 配置 Xcode 项目

1. 打开 Xcode 项目
2. 选择项目文件，然后选择主目标
3. 在 "Signing & Capabilities" 标签页中，点击 "+ Capability"
4. 添加 "Sign in with Apple" 功能

### 1.3 配置 app.json

确保 `app.json` 文件中包含以下配置：

```json
"ios": {
  "bundleIdentifier": "com.calmwatch",
  "entitlements": {
    "com.apple.developer.applesignin": ["Default"]
  }
}
```

## 2. 安装依赖

确保 `package.json` 中包含以下依赖：

```json
"dependencies": {
  "expo-apple-authentication": "~7.1.3",
  "expo-secure-store": "~14.0.1"
}
```

运行以下命令安装依赖：

```bash
yarn add expo-apple-authentication expo-secure-store
```

## 3. 实现 Apple 登录

### 3.1 创建 AuthStore

`app/models/AuthStore.ts` 中实现了 Apple 登录的核心逻辑：

```typescript
import { Instance, SnapshotOut, types } from "mobx-state-tree"
import * as AppleAuthentication from "expo-apple-authentication"
import * as SecureStore from "expo-secure-store"

export const AuthStoreModel = types
  .model("AuthStore")
  .props({
    isAuthenticated: types.optional(types.boolean, false),
    user: types.maybe(types.frozen<AppleAuthentication.AppleAuthenticationCredential>()),
  })
  .actions((self) => ({
    setAuthenticated(value: boolean) {
      self.isAuthenticated = value
    },
    setUser(user: AppleAuthentication.AppleAuthenticationCredential | null) {
      self.user = user || undefined
      self.isAuthenticated = !!user

      // 保存用户信息到安全存储
      if (user) {
        SecureStore.setItemAsync("user", JSON.stringify(user))
      } else {
        SecureStore.deleteItemAsync("user")
      }
    },
    async checkAuthStatus() {
      try {
        // 检查是否有保存的用户信息
        const userJson = await SecureStore.getItemAsync("user")
        if (userJson) {
          const user = JSON.parse(userJson)
          self.user = user
          self.isAuthenticated = true
          return true
        }
        return false
      } catch (error) {
        console.error("检查认证状态失败:", error)
        return false
      }
    },
    async signInWithApple() {
      try {
        const credential = await AppleAuthentication.signInAsync({
          requestedScopes: [
            AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
            AppleAuthentication.AppleAuthenticationScope.EMAIL,
          ],
        })

        // 登录成功
        this.setUser(credential)
        return { success: true, user: credential }
      } catch (e: any) {
        if (e.code === "ERR_CANCELED") {
          // 用户取消了登录
          return { success: false, error: "用户取消了登录" }
        } else {
          // console.error("Apple 登录错误:", e)
          return { success: false, error: e.message || "登录失败" }
        }
      }
    },
    async signOut() {
      this.setUser(null)
    },
  }))
```

### 3.2 集成到 RootStore

在 `app/models/RootStore.ts` 中集成 AuthStore：

```typescript
import { AuthStoreModel, AuthStore } from "./AuthStore"

export const RootStoreModel = types.model("RootStore").props({
  // 其他 store...
  authStore: types.optional(AuthStoreModel, {}),
})

export interface RootStore extends Instance<typeof RootStoreModel> {
  authStore: AuthStore
}
```

### 3.3 创建登录按钮组件

创建一个可重用的 Apple 登录按钮组件 `app/components/AppleSignInButton.tsx`：

```tsx
import * as AppleAuthentication from 'expo-apple-authentication';
import { observer } from 'mobx-react-lite';
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useStores } from '../models';

export const AppleSignInButton = observer(() => {
  const { authStore } = useStores()

  const handleAppleLogin = async () => {
    try {
      const result = await authStore.signInWithApple()
      if (result.success) {
        // 登录成功，可以跳转到首页或其他操作
        console.log('Apple 登录成功:', result.user)
      } else {
        // 处理登录失败
        console.error('Apple 登录失败:', result.error)
      }
    } catch (error) {
      console.error('Apple 登录异常:', error)
    }
  }

  return (
    <View style={styles.container}>
      <AppleAuthentication.AppleAuthenticationButton
        buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
        buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
        cornerRadius={5}
        style={styles.button}
        onPress={handleAppleLogin}
      />
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 44,
    marginVertical: 10,
  },
  button: {
    width: '100%',
    height: '100%',
  },
})
```

## 4. 在登录页面中使用

在登录页面中引入 Apple 登录按钮：

```tsx
import { AppleSignInButton } from '../components/AppleSignInButton'

// 在登录表单中添加
<AppleSignInButton />
```

## 5. 检查登录状态

在应用启动时检查用户是否已登录：

```typescript
// 在应用启动时调用
const checkAuthStatus = async () => {
  const isAuthenticated = await authStore.checkAuthStatus()
  if (isAuthenticated) {
    // 用户已登录，跳转到首页
    navigation.navigate('Main')
  } else {
    // 用户未登录，显示登录页面
    navigation.navigate('Login')
  }
}
```

## 6. 登出功能

实现登出功能：

```typescript
const handleSignOut = async () => {
  try {
    await authStore.signOut()
    // 登出成功，跳转到登录页面
    navigation.navigate('Login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}
```

## 7. 测试

### 7.1 在模拟器中测试

1. 在模拟器中打开设置
2. 登录您的 Apple ID
3. 运行应用并测试 Apple 登录功能

### 7.2 在真机上测试

1. 使用 TestFlight 或 Ad Hoc 分发方式安装应用到测试设备
2. 确保测试设备已登录 Apple ID
3. 测试登录流程

## 8. 常见问题

### 8.1 登录按钮不显示

- 确保已正确配置 Xcode 项目中的 "Sign in with Apple" 功能
- 检查设备是否运行 iOS 13.0 或更高版本
- 确保设备已登录 Apple ID

### 8.2 登录失败

- 检查 Apple Developer 控制台中的 App ID 配置
- 确保 `entitlements` 配置正确
- 检查网络连接

### 8.3 用户信息不完整

Apple 只会在用户首次授权时提供完整的用户信息。后续登录只会返回用户 ID。

## 9. 安全注意事项

1. 始终在服务器端验证从 Apple 收到的身份令牌
2. 使用 HTTPS 进行所有网络请求
3. 安全存储用户凭据

## 10. 参考文档

- [Apple 登录文档](https://developer.apple.com/sign-in-with-apple/)
- [expo-apple-authentication 文档](https://docs.expo.dev/versions/latest/sdk/apple-authentication/)
- [Apple 登录设计指南](https://developer.apple.com/design/human-interface-guidelines/sign-in-with-apple/overview/)
