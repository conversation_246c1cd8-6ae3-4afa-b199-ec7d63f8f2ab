# ECharts 初始化错误问题记录

## 问题描述

在 React Native 环境中，ECharts 初始化时出现以下错误：

```
ERROR  ECharts initialization error: [ReferenceError: Property 'document' doesn't exist] [Component Stack]
```

此错误表明 ECharts 尝试访问浏览器环境中特有的 `document` 对象，但在 React Native 的 JavaScript 运行环境中（通常是 Hermes 或 JavaScriptCore），该对象并不存在。

## 根本原因

ECharts 的核心渲染逻辑默认是为 Web 浏览器设计的，它依赖于 DOM API，特别是 `document` 对象来创建和管理图表容器（例如，通过 `document.createElement('canvas')`）。当 ECharts 在非浏览器环境（如 React Native）中初始化时，如果未使用特定于该环境的渲染器，它会回退到默认的浏览器行为，从而导致对 `document` 的访问失败。

在本项目中，虽然我们使用了 `@wuba/react-native-echarts` 库，它提供了适用于 React Native 的 `SVGRenderer`，但问题的根源在于 ECharts 模块的**多次初始化**。

在重构之前，多个图表组件（例如 `StressTrendCard`、`WeeklyStatsCard` 等）各自独立地导入和注册 ECharts 模块。这种做法会导致 `echarts.use()` 在应用的不同生命周期点被多次调用。这种重复注册会干扰 ECharts 的内部状态，可能导致其在某个时刻丢失了对 `SVGRenderer` 的正确配置，从而回退到默认的、依赖 `document` 的渲染模式，最终引发错误。

## 解决方案

为了从根本上解决这个问题，我们采取了**集中化管理 ECharts 实例**的策略。该方案确保 ECharts 的所有模块在整个应用中只被导入和注册一次。

具体步骤如下：

1.  **创建统一的 ECharts 配置文件**

    我们在 `app/utils/echarts.ts` 创建了一个文件，用于集中处理所有 ECharts 相关的导入和注册。

    ```typescript:app/utils/echarts.ts
    import * as echarts from "echarts/core"
    import { SVGRenderer } from "@wuba/react-native-echarts/svgChart"
    import { LineChart, BarChart, GaugeChart, CustomChart } from "echarts/charts"
    import { GridComponent, TooltipComponent, LegendComponent } from "echarts/components"

    echarts.use([
      SVGRenderer, // 必须首先注册 SVG 渲染器
      LineChart,
      BarChart,
      GaugeChart,
      CustomChart,
      GridComponent,
      TooltipComponent,
      LegendComponent,
    ])

    export default echarts
    ```

2.  **重构所有图表组件**

    我们修改了所有使用 ECharts 的组件，让它们不再自行导入和注册 ECharts 模块，而是直接从 `app/utils/echarts.ts` 导入已经配置好的 `echarts` 实例。

    例如，在 `WeeklyStatsCard.tsx` 中：

    **修改前：**
    ```typescript
    import * as echarts from 'echarts/core';
    import { BarChart } from 'echarts/charts';
    // ... 其他导入和注册
    echarts.use([BarChart, ...]);
    ```

    **修改后：**
    ```typescript
    import echarts from "../../../utils/echarts"
    // 不再有任何 echarts.use() 调用
    ```

通过这种方式，我们保证了 `SVGRenderer` 和所有图表组件在应用启动时被正确且唯一地注册。这避免了全局状态污染和因重复初始化导致的环境检测错误，从而彻底解决了 `Property 'document' doesn't exist` 的问题。