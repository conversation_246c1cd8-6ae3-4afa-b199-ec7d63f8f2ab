---
trigger: glob
globs: .txs
---

react nactive 函数组件格式示例:

import { observer } from "mobx-react-lite"
import { View, Text, TextStyle, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo } from "react"
import { aw } from "@/utils/adaptiveSize"

export const Banner = observer(function Banner() {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])

  return (
    <View style={$styles.screenContentContainer}></View>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      flex: 1,
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,
  }
}
