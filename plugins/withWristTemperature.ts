import { ConfigPlugin, withDangerousMod, withPlugins } from 'expo/config-plugins'
import * as fs from 'fs'
import * as path from 'path'

/**
 * Expo配置插件：添加手腕温度数据获取支持
 * 为iOS项目添加原生代码以支持Apple Watch Series 8+的手腕温度数据
 */
export const withWristTemperature: ConfigPlugin = (config) => {
  return withPlugins(config, [
    // 添加iOS原生模块
    withWristTemperatureIOS,
  ])
}

/**
 * iOS实现：添加手腕温度数据获取的原生模块
 */
const withWristTemperatureIOS: ConfigPlugin = (config) => {
  return withDangerousMod(config, [
    'ios',
    async (config) => {
      const iosProjectRoot = config.modRequest.platformProjectRoot
      const projectName = config.modRequest.projectName || 'calmwatch'
      
      // 创建WristTemperatureModule.h文件
      const headerContent = `//
//  WristTemperatureModule.h
//  ${projectName}
//
//  Created by Expo Plugin
//

#import <React/RCTBridgeModule.h>
#import <HealthKit/HealthKit.h>

@interface WristTemperatureModule : NSObject <RCTBridgeModule>

@end
`

      // 创建WristTemperatureModule.m文件
      const implementationContent = `//
//  WristTemperatureModule.m
//  ${projectName}
//
//  Created by Expo Plugin
//

#import "WristTemperatureModule.h"
#import <React/RCTLog.h>

@implementation WristTemperatureModule

RCT_EXPORT_MODULE();

// 获取手腕温度数据
RCT_EXPORT_METHOD(getWristTemperatureSamples:(NSDictionary *)options
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    if (![HKHealthStore isHealthDataAvailable]) {
        reject(@"healthkit_unavailable", @"HealthKit不可用", nil);
        return;
    }
    
    HKHealthStore *healthStore = [[HKHealthStore alloc] init];
    
    // 检查iOS版本和手腕温度数据类型可用性
    if (@available(iOS 16.0, *)) {
        HKQuantityType *wristTemperatureType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierAppleSleepingWristTemperature];
        
        if (!wristTemperatureType) {
            reject(@"type_unavailable", @"手腕温度数据类型不可用", nil);
            return;
        }
        
        // 检查权限
        HKAuthorizationStatus authStatus = [healthStore authorizationStatusForType:wristTemperatureType];
        if (authStatus == HKAuthorizationStatusSharingDenied) {
            reject(@"permission_denied", @"手腕温度数据访问权限被拒绝", nil);
            return;
        }
        
        // 请求权限（如果需要）
        if (authStatus == HKAuthorizationStatusNotDetermined) {
            NSSet *readTypes = [NSSet setWithObject:wristTemperatureType];
            [healthStore requestAuthorizationToShareTypes:nil readTypes:readTypes completion:^(BOOL success, NSError *error) {
                if (!success || error) {
                    reject(@"permission_request_failed", @"权限请求失败", error);
                    return;
                }
                [self fetchWristTemperatureData:healthStore options:options resolver:resolve rejecter:reject];
            }];
        } else {
            [self fetchWristTemperatureData:healthStore options:options resolver:resolve rejecter:reject];
        }
    } else {
        reject(@"ios_version_unsupported", @"需要iOS 16.0或更高版本才能获取手腕温度数据", nil);
    }
}

// 实际获取数据的方法
- (void)fetchWristTemperatureData:(HKHealthStore *)healthStore
                          options:(NSDictionary *)options
                         resolver:(RCTPromiseResolveBlock)resolve
                         rejecter:(RCTPromiseRejectBlock)reject
{
    if (@available(iOS 16.0, *)) {
        HKQuantityType *wristTemperatureType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierAppleSleepingWristTemperature];
        
        // 解析选项
        NSString *startDateString = options[@"startDate"];
        NSString *endDateString = options[@"endDate"];
        NSNumber *limit = options[@"limit"];
        BOOL ascending = [options[@"ascending"] boolValue];
        
        // 创建日期
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        [formatter setDateFormat:@"yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"];
        [formatter setTimeZone:[NSTimeZone timeZoneWithAbbreviation:@"UTC"]];
        
        NSDate *startDate = startDateString ? [formatter dateFromString:startDateString] : [[NSDate alloc] initWithTimeIntervalSinceNow:-14*24*60*60]; // 默认14天前
        NSDate *endDate = endDateString ? [formatter dateFromString:endDateString] : [NSDate date]; // 默认现在
        
        // 创建查询谓词
        NSPredicate *predicate = [HKQuery predicateForSamplesWithStartDate:startDate endDate:endDate options:HKQueryOptionStrictStartDate];
        
        // 创建排序描述符
        NSSortDescriptor *sortDescriptor = [NSSortDescriptor sortDescriptorWithKey:HKSampleSortIdentifierStartDate ascending:ascending];
        
        // 创建查询
        HKSampleQuery *query = [[HKSampleQuery alloc] initWithSampleType:wristTemperatureType
                                                               predicate:predicate
                                                                   limit:limit ? [limit integerValue] : HKObjectQueryNoLimit
                                                         sortDescriptors:@[sortDescriptor]
                                                          resultsHandler:^(HKSampleQuery *query, NSArray *samples, NSError *error) {
            if (error) {
                reject(@"query_failed", @"查询手腕温度数据失败", error);
                return;
            }
            
            NSMutableArray *results = [[NSMutableArray alloc] init];
            
            for (HKQuantitySample *sample in samples) {
                // 转换温度值为摄氏度
                HKUnit *celsiusUnit = [HKUnit degreeCelsiusUnit];
                double temperatureValue = [sample.quantity doubleValueForUnit:celsiusUnit];
                
                // 格式化日期
                NSString *startDateStr = [formatter stringFromDate:sample.startDate];
                NSString *endDateStr = [formatter stringFromDate:sample.endDate];
                
                NSDictionary *sampleDict = @{
                    @"id": sample.UUID.UUIDString,
                    @"value": @(temperatureValue),
                    @"startDate": startDateStr,
                    @"endDate": endDateStr,
                    @"unit": @"celsius",
                    @"metadata": sample.metadata ?: @{}
                };
                
                [results addObject:sampleDict];
            }
            
            resolve(results);
        }];
        
        [healthStore executeQuery:query];
    }
}

// 检查手腕温度数据可用性
RCT_EXPORT_METHOD(isWristTemperatureAvailable:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    if (![HKHealthStore isHealthDataAvailable]) {
        resolve(@{
            @"available": @NO,
            @"reason": @"HealthKit不可用"
        });
        return;
    }
    
    if (@available(iOS 16.0, *)) {
        HKQuantityType *wristTemperatureType = [HKQuantityType quantityTypeForIdentifier:HKQuantityTypeIdentifierAppleSleepingWristTemperature];
        
        resolve(@{
            @"available": @(wristTemperatureType != nil),
            @"reason": wristTemperatureType ? @"支持" : @"手腕温度数据类型不可用"
        });
    } else {
        resolve(@{
            @"available": @NO,
            @"reason": @"需要iOS 16.0或更高版本"
        });
    }
}

@end
`

      // 写入文件
      const headerPath = path.join(iosProjectRoot, `${projectName}/WristTemperatureModule.h`)
      const implementationPath = path.join(iosProjectRoot, `${projectName}/WristTemperatureModule.m`)
      
      console.log(`Writing header to: ${headerPath}`)
      console.log(`Writing implementation to: ${implementationPath}`)
      fs.writeFileSync(headerPath, headerContent)
      fs.writeFileSync(implementationPath, implementationContent)
      
      return config
    },
  ])
}