---
description: Guidelines for writing clean, maintainable, and human-readable code. Apply these rules when writing or reviewing code to ensure consistency and quality.
globs: 
alwaysApply: false
---
# 整洁代码指南

## 常量优于魔法数字
- 将硬编码的值替换为命名常量
- 使用描述性的常量名称来解释值的用途
- 将常量放在文件顶部或专用的常量文件中

## 有意义的命名
- 变量、函数和类的名称应揭示其用途
- 名称应解释某物为何存在以及如何使用
- 避免使用缩写，除非它们是普遍理解的

## 巧妙的注释
- 不要注释代码做了什么——让代码自文档化
- 使用注释来解释为什么某事要以特定方式完成
- 为 API、复杂算法和不明显的副作用添加文档

## 单一职责
- 每个函数应该只做一件事
- 函数应该小而专注
- 如果一个函数需要注释来解释它做了什么，那么它应该被拆分

## DRY (不要重复自己)
- 将重复的代码提取到可重用的函数中
- 通过适当的抽象共享通用逻辑
- 维护单一信息源

## 清晰的结构
- 将相关的代码放在一起
- 以逻辑层次组织代码
- 使用一致的文件和文件夹命名约定

## 封装
- 隐藏实现细节
- 暴露清晰的接口
- 将嵌套的条件语句移入命名良好的函数中

## 代码质量维护
- 持续重构
- 尽早修复技术债务
- 让代码比你发现时更整洁

## 测试
- 在修复 Bug 之前编写测试
- 保持测试的可读性和可维护性
- 测试边缘情况和错误条件

## 版本控制
- 编写清晰的提交信息
- 进行小而专注的提交
- 使用有意义的分支名称