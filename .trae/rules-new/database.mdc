---
description: Database best practices focusing on Prisma and Supabase integration
globs: prisma/**/*, src/db/**/*, **/*.prisma, supabase/**/*
alwaysApply: false
---
# 数据库最佳实践

## Prisma 设置
- 使用合理的 Schema 设计
- 实现合理的迁移 (Migrations)
- 使用合理的关系定义
- 配置合理的连接
- 实现合理的种子数据 (Seeding)
- 使用合理的客户端设置

## Prisma 模型
- 使用合理的模型命名
- 实现合理的关系
- 使用合理的字段类型
- 定义合理的索引
- 实现合理的约束
- 使用合理的枚举 (Enums)

## Prisma 查询
- 使用合理的查询优化
- 实现合理的过滤
- 使用合理的关系加载
- 合理地处理事务
- 实现合理的分页
- 使用合理的聚合 (Aggregations)

## Supabase 设置
- 配置合理的项目设置
- 实现合理的身份验证
- 使用合理的数据库设置
- 配置合理的存储
- 实现合理的策略 (Policies)
- 使用合理的客户端设置

## Supabase 安全性
- 实现合理的行级安全 (RLS) 策略
- 使用合理的身份验证
- 配置合理的权限
- 合理地处理敏感数据
- 实现合理的备份
- 使用合理的加密

## Supabase 查询
- 使用合理的查询优化
- 实现合理的过滤
- 使用合理的连接 (Joins)
- 合理地处理实时数据
- 实现合理的分页
- 使用合理的函数

## 数据库设计
- 使用合理的规范化 (Normalization)
- 实现合理的索引
- 使用合理的约束
- 定义合理的关系
- 实现合理的级联操作 (Cascades)
- 使用合理的数据类型

## 性能
- 使用合理的连接池
- 实现合理的缓存
- 使用合理的查询优化
- 合理地处理 N+1 查询问题
- 实现合理的批处理 (Batching)
- 监控性能指标

## 安全性
- 使用合理的身份验证
- 实现合理的授权
- 合理地处理敏感数据
- 使用合理的加密
- 实现合理的备份
- 监控安全问题

## 最佳实践
- 遵循数据库约定
- 使用合理的迁移
- 实现合理的版本控制
- 合理地处理错误
- 合理地记录 Schema
- 监控数据库健康状况