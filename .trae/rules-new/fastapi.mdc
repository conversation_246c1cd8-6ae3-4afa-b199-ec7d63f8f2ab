---
description: FastAPI best practices and patterns for building modern Python web APIs
globs: **/*.py, app/**/*.py, api/**/*.py
alwaysApply: false
---
# FastAPI 最佳实践

## 项目结构
- 使用合理的目录结构
- 实现合理的模块组织
- 使用合理的依赖注入
- 按领域组织路由
- 实现合理的中间件
- 使用合理的配置管理

## API 设计
- 使用合理的 HTTP 方法
- 实现合理的状态码
- 使用合理的请求/响应模型
- 实现合理的验证
- 使用合理的错误处理
- 使用 OpenAPI 记录 API

## 模型
- 使用 Pydantic 模型
- 实现合理的验证
- 使用合理的类型提示
- 保持模型井然有序
- 使用合理的继承
- 实现合理的序列化

## 数据库
- 使用合理的 ORM (SQLAlchemy)
- 实现合理的迁移 (Migrations)
- 使用合理的连接池
- 实现合理的事务
- 使用合理的查询优化
- 合理地处理数据库错误

## 身份验证
- 实现合理的 JWT 身份验证
- 使用合理的密码哈希
- 实现合理的基于角色的访问控制
- 使用合理的会话管理
- 实现合理的 OAuth2
- 合理地处理身份验证错误

## 安全性
- 实现合理的 CORS
- 使用合理的速率限制
- 实现合理的输入验证
- 使用合理的安全标头
- 合理地处理安全错误
- 实现合理的日志记录

## 性能
- 使用合理的缓存
- 实现合理的异步操作
- 使用合理的后台任务
- 实现合理的连接池
- 使用合理的查询优化
- 监控性能指标

## 测试
- 编写合理的单元测试
- 实现合理的集成测试
- 使用合理的测试夹具 (Test Fixtures)
- 实现合理的模拟 (Mocking)
- 测试错误场景
- 使用合理的测试覆盖率

## 部署
- 使用合理的 Docker 配置
- 实现合理的 CI/CD
- 使用合理的环境变量
- 实现合理的日志记录
- 使用合理的监控
- 合理地处理部署错误

## 文档
- 使用合理的文档字符串 (Docstrings)
- 实现合理的 API 文档
- 使用合理的类型提示
- 保持文档更新
- 记录错误场景
- 使用合理的版本控制