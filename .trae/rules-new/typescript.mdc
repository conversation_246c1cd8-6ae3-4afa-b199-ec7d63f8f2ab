---
description: TypeScript coding standards and best practices for modern web development
globs: **/*.ts, **/*.tsx, **/*.d.ts
alwaysApply: false
---
# TypeScript 最佳实践

## 类型系统
- 对于对象定义，优先使用接口 (interfaces) 而非类型别名 (types)
- 对联合类型 (unions)、交叉类型 (intersections) 和映射类型 (mapped types) 使用类型别名 (type)
- 避免使用 `any`，对未知类型优先使用 `unknown`
- 使用严格的 TypeScript 配置
- 利用 TypeScript 内置的工具类型 (utility types)
- 对可重用的类型模式使用泛型 (generics)

## 命名约定
- 对类型名称和接口使用 PascalCase (大驼峰命名法)
- 对变量和函数使用 camelCase (小驼峰命名法)
- 对常量使用 UPPER_CASE (全大写字母)
- 使用带有助动词的描述性名称 (例如：`isLoading`, `hasError`)
- 为 React 组件的 props 接口添加 'Props' 前缀 (例如：`ButtonProps`)

## 代码组织
- 将类型定义保持在靠近其使用位置的地方
- 当类型和接口被共享时，从专用的类型文件中导出它们
- 使用桶式导出 (barrel exports, 即 `index.ts`) 来组织导出
- 将共享类型放置在 `types` 目录中
- 将组件的 props 与其组件放在一起

## 函数
- 对公共函数使用显式的返回类型
- 对回调函数和方法使用箭头函数
- 使用自定义错误类型实现合理的错误处理
- 对复杂的类型场景使用函数重载
- 优先使用 async/await 而非 Promises

## 最佳实践
- 在 tsconfig.json 中启用严格模式 (strict mode)
- 对不可变属性使用 readonly
- 利用可辨识联合 (discriminated unions) 来确保类型安全
- 对运行时类型检查使用类型守卫 (type guards)
- 实现合理的空值检查 (null checking)
- 除非必要，否则避免使用类型断言 (type assertions)

## 错误处理
- 为特定领域的错误创建自定义错误类型
- 对可能失败的操作使用 Result 类型
- 实现合理的错误边界 (Error Boundaries)
- 使用带有类型化 catch 子句的 try-catch 块
- 合理地处理 Promise rejections

## 模式
- 对复杂对象的创建使用构建器模式 (Builder pattern)
- 对数据访问实现仓库模式 (Repository pattern)
- 对对象创建使用工厂模式 (Factory pattern)
- 利用依赖注入 (dependency injection)
- 使用模块模式 (Module pattern) 进行封装