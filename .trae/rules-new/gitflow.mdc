---
description: Gitflow Workflow Rules. These rules should be applied when performing git operations.
globs: 
alwaysApply: false
---
# Gitflow 工作流规则

## 主要分支

### main (或 master)
- 包含生产就绪的代码
- 绝不直接提交到 main
- 仅接受来自以下分支的合并：
  - hotfix/* 分支
  - release/* 分支
- 每次合并后必须使用版本号进行标记 (tag)

### develop
- 主要开发分支
- 包含最新的已交付开发更改
- feature 分支的源分支
- 绝不直接提交到 develop

## 支持分支

### feature/*
- 从以下分支创建： develop
- 合并回以下分支： develop
- 命名约定： feature/[issue-id]-descriptive-name
- 示例： feature/123-user-authentication
- 在创建 PR (Pull Request) 之前必须与 develop 保持最新
- 合并后删除

### release/*
- 从以下分支创建： develop
- 合并回以下分支：
  - main
  - develop
- 命名约定： release/vX.Y.Z
- 示例： release/v1.2.0
- 仅包含 Bug 修复、文档和与发布相关的任务
- 不包含新功能
- 合并后删除

### hotfix/*
- 从以下分支创建： main
- 合并回以下分支：
  - main
  - develop
- 命名约定： hotfix/vX.Y.Z
- 示例： hotfix/v1.2.1
- 仅用于紧急的生产环境修复
- 合并后删除

## 提交信息

- 格式： `type(scope): description`
- 类型 (Types)：
  - feat: 新功能
  - fix: Bug 修复
  - docs: 文档更改
  - style: 格式化、缺少分号等
  - refactor: 代码重构
  - test: 添加测试
  - chore: 维护任务

## 版本控制

### 语义化版本控制 (Semantic Versioning)
- MAJOR 版本：用于不兼容的 API 更改
- MINOR 版本：用于向后兼容的功能添加
- PATCH 版本：用于向后兼容的 Bug 修复

## Pull Request 规则

1. 所有更改必须通过 Pull Request 进行
2. 需要的批准数：至少 1 个
3. CI 检查必须通过
4. 禁止直接提交到受保护的分支 (main, develop)
5. 分支在合并前必须是最新状态
6. 合并后删除分支

## 分支保护规则

### main & develop
- 要求 Pull Request 审查
- 要求状态检查通过
- 要求分支是最新状态
- 将管理员包含在限制内
- 禁止强制推送 (force push)
- 禁止删除

## 发布流程

1. 从 develop 创建 release 分支
2. 提升版本号
3. 修复任何与发布相关的特定问题
4. 创建到 main 的 PR
5. 合并到 main 后：
   - 标记发布版本 (tag)
   - 将更改合并回 develop
   - 删除 release 分支

## 热修复流程 (Hotfix Process)

1. 从 main 创建 hotfix 分支
2. 修复问题
3. 提升 PATCH 版本号
4. 创建到 main 的 PR
5. 合并到 main 后：
   - 标记发布版本 (tag)
   - 将更改合并回 develop
   - 删除 hotfix 分支