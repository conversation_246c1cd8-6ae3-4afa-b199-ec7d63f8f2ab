---
description: React best practices and patterns for modern web applications
globs: **/*.tsx, **/*.jsx, components/**/*
alwaysApply: false
---
# React 最佳实践

## 组件结构
- 优先使用函数组件而非类组件
- 保持组件小而专注
- 将可重用逻辑提取到自定义 Hook 中
- 使用组合而非继承
- 使用 TypeScript 实现合理的 prop 类型
- 将大型组件拆分为更小、更专注的组件

## Hooks
- 遵循 Hooks 规则
- 对可重用逻辑使用自定义 Hook
- 保持 Hook 专注且简单
- 在 useEffect 中使用适当的依赖项数组
- 在需要时在 useEffect 中实现清理逻辑
- 避免嵌套 Hook

## 状态管理
- 对本地组件状态使用 useState
- 对复杂状态逻辑使用 useReducer
- 对共享状态使用 Context API
- 尽可能将状态保持在靠近其使用位置的地方
- 通过合理的状态管理避免 prop drilling (属性逐层传递)
- 仅在必要时使用状态管理库

## 性能
- 实现合理的 Memoization (useMemo, useCallback)
- 对昂贵的组件使用 React.memo
- 避免不必要的重新渲染
- 实现合理的懒加载
- 在列表中使用合理的 key 属性
- 分析并优化渲染性能

## 表单
- 对表单输入使用受控组件
- 实现合理的表单验证
- 合理地处理表单提交状态
- 显示适当的加载和错误状态
- 对复杂表单使用表单库
- 为表单实现合理的无障碍性 (Accessibility)

## 错误处理
- 实现错误边界 (Error Boundaries)
- 合理地处理异步错误
- 显示用户友好的错误消息
- 实现合理的回退 UI (Fallback UI)
- 适当地记录错误
- 优雅地处理边缘情况

## 测试
- 为组件编写单元测试
- 为复杂流程实现集成测试
- 使用 React Testing Library
- 测试用户交互
- 测试错误场景
- 实现合理的模拟数据

## 无障碍性 (Accessibility)
- 使用语义化的 HTML 元素
- 实现合理的 ARIA 属性
- 确保键盘导航
- 使用屏幕阅读器进行测试
- 处理焦点管理
- 为图片提供合理的 alt 文本

## 代码组织
- 将相关组件分组在一起
- 使用合理的文件命名约定
- 实现合理的目录结构
- 将样式保持在靠近组件的位置
- 使用合理的导入/导出
- 记录复杂的组件逻辑