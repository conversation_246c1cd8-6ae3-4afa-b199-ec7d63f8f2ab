---
description: Code Quality Guidelines
globs: 
alwaysApply: false
---
# 代码质量指南

## 验证信息
在呈现信息之前务必进行验证。不要在没有明确证据的情况下做出假设或推测。

## 逐文件更改
逐个文件进行更改，并给我机会发现错误。

## 无需道歉
绝不使用道歉。

## 无需理解反馈
避免在注释或文档中提供关于理解的反馈。

## 无需空格建议
不要建议进行空格相关的更改。

## 无需总结
不要总结所做的更改。

## 无需发明
除了明确要求的内容外，不要自行发明更改。

## 无需不必要的确认
如果上下文中已提供了相关信息，不要再次要求确认。

## 保留现有代码
不要删除无关的代码或功能。注意保留现有结构。

## 单块编辑
对于同一文件，以单块形式提供所有编辑内容，而不是分多步说明或解释。

## 无需检查实现
不要要求用户验证在所提供上下文中可见的实现。

## 无需不必要的更新
如果没有实际需要修改的地方，不要建议更新或更改文件。

## 提供真实文件链接
始终提供指向真实文件的链接，而不是 x.md。

## 无需展示当前实现
除非特别要求，否则不要展示或讨论当前实现。