---
description: Vue.js best practices and patterns for modern web applications
globs: **/*.vue, **/*.ts, components/**/*
alwaysApply: false
---
# Vue.js 最佳实践

## 组件结构
- Vue3 优先使用组合式 API (Composition API) 而非选项式 API (Options API)
- 保持组件小而专注
- 使用合理的 TypeScript 集成
- 实现合理的 props 验证
- 使用合理的 emit 声明
- 保持模板逻辑最小化

## 组合式 API (Composition API)
- 合理使用 ref 和 reactive
- 实现合理的生命周期钩子
- 对可重用逻辑使用可组合函数 (composables)
- 保持 setup 函数简洁
- 使用合理的计算属性 (computed properties)
- 实现合理的侦听器 (watchers)

## 状态管理
- 使用 Pinia 进行状态管理
- 保持 store 模块化
- 使用合理的状态组合
- 实现合理的 actions
- 使用合理的 getters
- 合理地处理异步状态

## 性能
- 使用合理的组件懒加载
- 实现合理的缓存
- 使用合理的计算属性
- 避免不必要的侦听器
- 合理使用 v-show 与 v-if
- 实现合理的 key 管理

## 路由
- 合理使用 Vue Router
- 实现合理的导航守卫
- 使用合理的路由元信息字段 (route meta fields)
- 合理地处理路由参数
- 实现合理的懒加载
- 使用合理的导航方法

## 表单
- 合理使用 v-model
- 实现合理的验证
- 合理地处理表单提交
- 显示合理的加载状态
- 使用合理的错误处理
- 实现合理的表单重置

## TypeScript 集成
- 使用合理的组件类型定义
- 实现合理的 prop 类型
- 使用合理的 emit 声明
- 处理合理的类型推断
- 使用合理的可组合函数类型
- 实现合理的 store 类型

## 测试
- 编写合理的单元测试
- 实现合理的组件测试
- 合理使用 Vue Test Utils
- 合理地测试可组合函数
- 实现合理的模拟 (mocking)
- 测试异步操作

## 最佳实践
- 遵循 Vue 风格指南
- 使用合理的命名约定
- 保持组件井然有序
- 实现合理的错误处理
- 使用合理的事件处理
- 记录复杂逻辑

## 构建与工具
- 使用 Vite 进行开发
- 配置合理的构建设置
- 使用合理的环境变量
- 实现合理的代码分割
- 使用合理的资源处理
- 配置合理的优化