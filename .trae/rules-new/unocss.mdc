---
description: Tailwind CSS and UI component best practices for modern web applications
globs: **/*.css, **/*.tsx, **/*.jsx, tailwind.config.js, tailwind.config.ts
alwaysApply: false
---
# UnoCSS 最佳实践

## 项目设置
- 使用合理的 UnoCSS 配置 (`uno.config.ts` 或类似文件)
- 合理地配置主题 (theme) (在预设或配置中)
- 理解 UnoCSS 的按需生成机制 (无需手动配置 Purge)
- 使用合理的预设 (presets) 和规则 (rules) 集成
- 配置自定义间距、断点和颜色
- 设置合理的调色板

## 组件样式
- 优先使用原子化 CSS 工具类而非自定义 CSS
- 在需要时使用 UnoCSS 的快捷方式 (shortcuts) 对相关工具类进行分组
- 使用合理的响应式设计变体 (variants) (例如 `sm:`, `md:`)
- 合理地实现暗黑模式 (使用 `dark:` 变体)
- 使用合理的状态变体 (例如 `hover:`, `focus:`)
- 保持组件样式一致

## 布局
- 有效地使用 Flexbox 和 Grid 工具类
- 实现合理的间距系统
- 在需要时使用容器查询变体
- 实现合理的响应式断点
- 使用合理的内边距 (padding) 和外边距 (margin) 工具类
- 实现合理的对齐工具类

## 排版
- 使用合理的字体大小工具类
- 实现合理的行高
- 使用合理的字体粗细工具类
- 合理地配置自定义字体
- 使用合理的文本对齐
- 实现合理的文本装饰

## 颜色
- 使用语义化的颜色命名
- 实现合理的颜色对比度
- 有效地使用透明度工具类
- 合理地配置自定义颜色
- 使用合理的渐变工具类
- 实现合理的悬停状态 (`hover:`)

## 组件
- 考虑使用与 UnoCSS 兼容的组件库
- 合理地扩展组件
- 保持组件变体一致
- 实现合理的动画
- 使用合理的过渡工具类
- 考虑无障碍性 (Accessibility)

## 响应式设计
- 使用移动优先 (mobile-first) 的方法
- 实现合理的断点
- 有效地使用容器查询变体
- 合理地处理不同的屏幕尺寸
- 实现合理的响应式排版
- 使用合理的响应式间距

## 性能
- 利用 UnoCSS 的按需生成特性
- 最小化自定义 CSS
- 使用合理的缓存策略
- 实现合理的代码分割 (code splitting)
- 针对生产环境进行优化
- 监控打包大小 (bundle size)

## 最佳实践
- 遵循命名约定
- 保持样式井然有序
- 使用合理的文档
- 实现合理的测试
- 遵循无障碍性指南
- 使用合理的版本控制