---
description: Provides additional instructions for React Native Expo development, such as using TypeScript, StyleSheet for styling, and Expo's secure store for sensitive data.
globs: src/**/*.*
---
- Use TypeScript for type safety
- Implement proper styling using StyleSheet
- Utilize Expo's vector icons
- Use Expo's secure store for sensitive data
- Implement proper offline support
- Follow React Native best practices for performance
- Use Expo's OTA updates for quick deployments