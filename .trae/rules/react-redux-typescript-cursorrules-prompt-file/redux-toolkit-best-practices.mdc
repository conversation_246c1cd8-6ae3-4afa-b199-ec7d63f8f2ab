---
description: Applies Redux Toolkit best practices for efficient Redux development.
globs: src/store/**/*.ts
---
- Use Redux Toolkit for efficient Redux development.
- Implement slice pattern for organizing Redux code.
- Utilize createAsyncThunk for handling async actions.
- Use selectors for accessing state in components.
- Use Redux hooks (useSelector, useDispatch) in components.
- Follow Redux style guide for naming conventions