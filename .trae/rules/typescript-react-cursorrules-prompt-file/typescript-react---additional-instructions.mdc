---
description: Provides additional instructions for TypeScript React development, including file extensions, strict checks, and error handling.
globs: **/*.tsx
---
- Use .tsx extension for files with JSX
- Implement strict TypeScript checks
- Utilize React.lazy and Suspense for code-splitting
- Use type inference where possible
- Implement error boundaries for robust error handling
- Follow React and TypeScript best practices and naming conventions
- Use ESLint with TypeScript and React plugins for code quality