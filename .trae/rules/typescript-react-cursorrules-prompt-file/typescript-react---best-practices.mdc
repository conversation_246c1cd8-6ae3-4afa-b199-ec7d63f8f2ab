---
description: Enforces TypeScript React best practices related to using React.FC, hooks, interfaces, and optimization techniques.
globs: src/**/*.*
---
- Use React.FC for functional components with props
- Utilize useState and useEffect hooks for state and side effects
- Implement proper TypeScript interfaces for props and state
- Use React.memo for performance optimization when needed
- Implement custom hooks for reusable logic
- Utilize TypeScript's strict mode