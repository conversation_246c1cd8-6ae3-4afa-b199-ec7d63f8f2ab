---
description: Enforces best practices for React Native Expo development within the src directory, promoting maintainable and efficient code.
globs: src/**/*.*
---
- Use functional components with hooks.
- Leverage Expo SDK features and APIs.
- Implement navigation using Expo Router.
- Manage assets with Expo's asset system for images and fonts.
- Ensure robust error handling and crash reporting.
- Utilize Expo's push notification system.
- Adopt TypeScript for type safety.
- Apply consistent styling using StyleSheet.
- Incorporate Expo's vector icons.
- Secure sensitive data with Expo's SecureStore.
- Implement proper offline support.
- Optimize performance following React Native best practices.
- Deploy updates using Expo's OTA mechanism.
- Style components using NativeWind.