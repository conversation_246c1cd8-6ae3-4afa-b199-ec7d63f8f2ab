---
description: Provides general instructions for project management, including terminal commands, package management, and file operations.
globs: *.*
---
- Use PowerShell for terminal commands.
- Before installing a new package, check if it's already installed:
  Get-ChildItem -Recurse -Filter package-name
- If installed, upgrade using:
  expo upgrade <package-name>
  or
  npm install <package-name>
  if not supported by Expo.
- Use PowerShell commands to manage the project, e.g., moving and renaming files:
  Move-Item -Path .\old\path\file.txt -Destination .\new\path\newname.txt
- If unsure about the current structure or details, use PowerShell to list out necessary information:
  Get-ChildItem -Recurse
- Utilize official Expo libraries and upgrade them using Expo's commands.
- Avoid deleting existing functionality or files without a valid reason.
- Follow the recommended folder structure and maintain organized code for scalability and readability.
- Implement navigation using Expo Router for clean and declarative routing.