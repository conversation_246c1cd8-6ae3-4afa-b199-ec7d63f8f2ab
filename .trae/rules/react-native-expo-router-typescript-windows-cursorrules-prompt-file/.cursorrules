// React Native Expo .cursorrules

// React Native Expo Best Practices

const reactNativeExpoBestPractices = [
  "Use functional components with hooks.",
  "Leverage Expo SDK features and APIs.",
  "Implement navigation using Expo Router.",
  "Manage assets with Expo's asset system for images and fonts.",
  "Ensure robust error handling and crash reporting.",
  "Utilize Expo's push notification system.",
  "Adopt TypeScript for type safety.",
  "Apply consistent styling using StyleSheet.",
  "Incorporate Expo's vector icons.",
  "Secure sensitive data with Expo's SecureStore.",
  "Implement proper offline support.",
  "Optimize performance following React Native best practices.",
  "Deploy updates using Expo's OTA mechanism.",
  "Style components using NativeWind.",
];

// Folder Structure

const folderStructure = `
assets/
src/
  components/
  screens/
  navigation/
  hooks/
  utils/
app/
  _layout.tsx
  index.tsx
App.js
app.json
`;

// Package Version Compatibility Notes

const packageCompatibilityNotes = [
  "NativeWind and Tailwind CSS compatibility:",
  "- Use nativewind@2.0.11 with tailwindcss@3.3.2.",
  "- Higher versions may cause 'process(css).then(cb)' errors.",
  "- If errors occur, remove both packages and reinstall specific versions:",
  "  npm remove nativewind tailwindcss",
  "  npm install nativewind@2.0.11 tailwindcss@3.3.2",

  "Babel configuration for NativeWind:",
  "- Include 'nativewind/babel' in the plugins array.",
  "- Avoid using jsxImportSource in presets.",
  "- Ensure 'react-native-reanimated/plugin' follows 'nativewind/babel'."
];

// Additional Instructions

const additionalInstructions = [
  "Use PowerShell for terminal commands.",
  "Before installing a new package, check if it's already installed:",
  "  Get-ChildItem -Recurse -Filter package-name",
  "If installed, upgrade using:",
  "  expo upgrade <package-name>",
  "or",
  "  npm install <package-name>",
  "if not supported by Expo.",
  "Use PowerShell commands to manage the project, e.g., moving and renaming files:",
  "  Move-Item -Path .\\old\\path\\file.txt -Destination .\\new\\path\\newname.txt",
  "If unsure about the current structure or details, use PowerShell to list out necessary information:",
  "  Get-ChildItem -Recurse",
  "Utilize official Expo libraries and upgrade them using Expo's commands.",
  "Avoid deleting existing functionality or files without a valid reason.",
  "Follow the recommended folder structure and maintain organized code for scalability and readability.",
  "Implement navigation using Expo Router for clean and declarative routing."
];

