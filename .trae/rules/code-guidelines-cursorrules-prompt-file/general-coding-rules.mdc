---
description: Applies general coding rules across all file types to maintain code quality, consistency, and prevent common errors.
globs: **/*.*
alwaysApply: false
---
- 始终在呈现信息之前验证其准确性。不要在没有明确证据的情况下做出假设或推测。
- 逐个文件进行修改，并给我机会发现可能的错误。
- 永远不要使用道歉。
- 避免在注释或文档中提供关于“理解”的反馈。
- 不要建议与空格相关的更改。
- 不要对所做的更改进行总结。
- 除了明确请求的内容外，不要发明其他更改。
- 如果上下文中已经提供了相关信息，不要再次要求确认。
- 不要删除无关的代码或功能。注意保留现有的结构。
- 在同一文件的所有编辑内容应以单块形式提供，而不是分多步说明或解释。
- 不要让用户验证在上下文中已可见的实现。
- 如果没有实际需要修改的地方，不要建议更新或更改文件。
- 始终提供指向真实文件的链接，而不是上下文生成的文件。
- 除非特别要求，否则不要展示或讨论当前实现。
- 记住检查上下文生成的文件，以获取当前文件的内容和实现。
- 优先使用描述性强、明确的变量名，而不是简短且模糊的名称，以提高代码可读性。
- 遵守项目中现有的编码风格，以确保一致性。
- 在提出更改建议时，请根据适用情况优先考虑代码性能。
- 在修改或建议代码更改时，始终考虑安全性影响。
- 为新增或修改的代码建议或包含适当的单元测试。
- 在必要时实现健壮的错误处理和日志记录。
- 鼓励模块化设计原则，以提高代码的可维护性和可重用性。
- 确保建议的更改与项目指定的语言或框架版本兼容。
- 将硬编码值替换为命名常量，以提高代码的清晰度和可维护性。
- 在实现逻辑时，始终考虑并处理潜在的边界情况。
- 尽可能包含断言，以验证假设并在早期捕获潜在错误。