# 代码规范 .cursorrules 提示文件

作者：Hamza Farhan

## 您可以构建的内容

**代码分析工具**：开发一个工具，用于分析代码是否符合 `.cursorrules` AI 文件中设定的规则。该工具会突出显示违反规则的情况，例如缺少错误处理、测试覆盖率不足以及编码风格不匹配等问题。

**自动重构服务**：创建一项服务，能够逐文件地自动重构代码，同时遵守以下准则：避免空格建议、杜绝魔法数字、确保模块化设计。

**项目一致性检查器**：构建一个工具，用于检查项目中多个文件的编码风格一致性、性能优先级和安全性考量，同时避免不必要的更新。

**开发者助手扩展**：设计一款浏览器或 IDE 扩展程序，在开发者编辑代码时提供实时反馈，强调使用明确的变量名称、避免道歉、确保无需不必要的确认。

**代码优化平台**：搭建一个平台，提供性能改进和模块化设计策略的建议，同时确保版本兼容性，并优先考虑安全第一的方法。

**单元测试建议应用**：创建一款应用，能够为新增或修改的代码自动生成单元测试，重点关注覆盖率，除非特别请求，否则不会展示或讨论当前实现。

**版本兼容性分析器**：开发一个分析器，用于检查代码更改是否与项目指定的语言或框架版本兼容，以确保顺利集成和功能正常。

**边界情况识别器**：构建一个工具，扫描代码以识别潜在的边界情况，并提供建议的处理策略，确保强大的错误处理和日志记录机制。

**上下文感知代码审查平台**：创建一个代码审查平台，利用生成的上下文文件对代码编辑提供建议，确保无需不必要的确认并保留现有文件。

**模块化设计教育平台**：开发一个学习平台，教育开发者如何实施和鼓励模块化设计原则，专注于代码的可重用性和可维护性。

**断言验证器**：构建一个服务，集成到 CI/CD 管道中，检查代码中断言的存在及其正确使用，从而提高验证准确性并及早发现错误。

**硬编码值检测器**：创建一个工具，扫描代码库中的魔法数字，并建议将其替换为命名常量，以提高代码的清晰度和可维护性。

**错误处理增强器**：设计一个插件或独立工具，为代码中的错误处理机制提供建议，推荐强大的日志记录实践。

---

## **优势**

---

## **简介**

在协作项目中工作的开发者可以通过建立清晰高效的代码审查和更新实践而受益，从而确保团队内部代码的一致性、安全性和可维护性。

---

## **.cursorrules 提示概述**

`.cursorrules` 文件列出了在编辑代码或提出更改建议时应遵循的一系列规则和指南。它强调了信息验证、逐文件进行更改、保留现有代码、避免不必要的确认或更新。文件建议避免使用道歉、不必要的空格更改或总结已做的更改。重点在于提供真实的文件链接、使用明确的变量名称以及遵循一致的编码风格。性能、安全性、错误处理、模块化设计、版本兼容性、边界情况和测试覆盖率是优先考虑的因素。文件还反对使用“魔法数字”，并鼓励通过断言及早捕获错误。
