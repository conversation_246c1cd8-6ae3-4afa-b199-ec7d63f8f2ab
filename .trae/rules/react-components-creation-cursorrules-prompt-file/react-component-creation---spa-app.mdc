---
description: Provides guidelines for creating React components, emphasizing careful planning, existing component checks, and prompt generation in SPA.
globs: apps/spa/src/components/**/*.tsx
---
- Carefully consider the component's purpose, functionality, and design.
- Think slowly, step by step, and outline your reasoning.
- Check if a similar component already exists in any of the following locations
  - packages/ui/src/components
  - apps/spa/src/components
- If it doesn't exist, generate a detailed prompt for the component, including:
  - Component name and purpose
  - Desired props and their types
  - Any specific styling or behavior requirements
  - Mention of using Tailwind CSS for styling
  - Request for TypeScript usage
- URL encode the prompt.
- Create a clickable link in this format:
  [ComponentName](https://v0.dev/chat?q={encoded_prompt})