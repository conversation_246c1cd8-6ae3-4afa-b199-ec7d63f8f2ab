---
description: Provides an example prompt template for generating react components with specific instructions.
globs: packages/ui/src/components/**/*.tsx
---
- Example prompt template:
  "Create a React component named {ComponentName} using TypeScript and Tailwind CSS. It should {description of functionality}. Props should include {list of props with types}. The component should {any specific styling or behavior notes}. Please provide the full component code."
- Remember to replace placeholders like <ui_package_path> and <app_package_alias> with the actual values used in your project.