---
description: Specifies steps to adapt generated React components to the SPA project structure after initial creation.
globs: apps/spa/src/components/**/*.tsx
---
- After generating, adapt the component to fit our project structure:
  - Import
    - common shadcn/ui components from <ui_package_alias>@repo/ui/components/ui/</ui_package_alias>
    - app specific components from <app_package_alias>@/components</app_package_alias>
  - Ensure it follows our existing component patterns
  - Add any necessary custom logic or state management