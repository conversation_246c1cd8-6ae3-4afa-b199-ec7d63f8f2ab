import { FC, useRef, useMemo, useEffect } from "react"
import { View, ViewStyle, TextStyle, Dimensions } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, ah, af } from "@/utils/adaptiveSize"
import { timeUtils } from "@/utils/timeUtils"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"
import { HrvDataPoint, StressDataPoint } from "@/data/types"
import { typography } from "@/theme/typography"

const { width: screenWidth } = Dimensions.get('window')

// 状态颜色映射
const statusColors = {
  excellent: '#11D3BC', // 状态优秀 - 青色
  normal: '#9896F1',    // 状态正常 - 深蓝色
  attention: '#FFAA64', // 注意压力 - 中蓝色
  overload: '#F85F73',  // 压力过载 - 红色
}

// 状态标签映射
const statusLabels = {
  excellent: '状态优秀',
  normal: '状态正常',
  attention: '注意压力',
  overload: '压力过载',
}

interface StressTrendCardProps {
  todayHrvData: HrvDataPoint[]
  todayStressData: StressDataPoint[]
  sleepTimeRange: [string, string]
}

export const StressTrendCard: FC<StressTrendCardProps> = ({ todayHrvData, todayStressData, sleepTimeRange }) => {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)
  const hrvChartRef = useRef<any>(null)
  const stressChartRef = useRef<any>(null)

  const CHART_WIDTH = screenWidth - aw(64)
  const CHART_HEIGHT = ah(160)

  // HRV趋势图配置
  const hrvChartOption = useMemo(() => {
    // 为每种状态创建动态的rich样式
    const richStyles: any = {
      value: {
        fontSize: af(12),
        color: '#333',
        fontWeight: '600'
      },
      time: {
        fontSize: af(11),
        color: '#666',
        lineHeight: af(18)
      }
    }

    // 为每种状态添加对应颜色的样式
    Object.keys(statusColors).forEach(status => {
      richStyles[status] = {
        fontSize: af(12),
        fontWeight: 'bold',
        color: statusColors[status as keyof typeof statusColors]
      }
    })

    return {
      tooltip: {
        trigger: 'item',
        triggerOn: 'click',
        confine: true,
        backgroundColor: '#FFFFFF',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          fontSize: af(12),
          lineHeight: af(16),
        },
        padding: [ah(6), aw(10)],
        extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 9999;',
        formatter: (params: any) => {
          const dataIndex = params.dataIndex
          const dataItem = todayHrvData[dataIndex]
          const statusLabel = statusLabels[dataItem.status as keyof typeof statusLabels]

          // 使用状态作为rich样式的key，实现动态颜色
          return `{${dataItem.status}|${statusLabel}} {value|${dataItem.value}ms}\n{time|HRV · ${dataItem.time}}`
        },
        rich: richStyles
      },
      grid: {
        left: aw(16),
        right: aw(16),
        top: ah(6),
        bottom: ah(30),
        containLabel: false,
      },
      xAxis: {
        type: 'category',
        data: todayHrvData.map(item => item.time), // 使用hrvData中的时间作为X轴标签
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#8489B4',
          fontSize: aw(12),
          margin: ah(12),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#BCBFD0',
            type: 'dashed',
          },
        },
      },
      yAxis: {
        type: 'value',
        show: false,
        min: 0,
        max: 110,
      },
      series: [
        {
          type: 'line',
          data: todayHrvData.map(item => ({
            value: item.value,
            itemStyle: {
              color: '#FFFFFF',
              borderColor: statusColors[item.status as keyof typeof statusColors],
              borderWidth: aw(2),
            },
            symbol: 'circle',
            symbolSize: aw(12),
          })),
          lineStyle: {
            color: '#8489B4',
            width: aw(1),
          },
          smooth: false,
          showSymbol: true,
        },
      ],
    }
  }, [todayHrvData])

  // 实时压力柱状图配置
  const stressChartOption = useMemo(() => {
    // 为每种状态创建动态的rich样式
    const richStyles: any = {
      value: {
        fontSize: af(12),
        color: '#333',
        fontWeight: '600'
      },
      time: {
        fontSize: af(11),
        color: '#666',
        lineHeight: af(18)
      }
    }

    // 为每种状态添加对应颜色的样式
    Object.keys(statusColors).forEach(status => {
      richStyles[status] = {
        fontSize: af(12),
        fontWeight: 'bold',
        color: statusColors[status as keyof typeof statusColors]
      }
    })

    return {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        triggerOn: 'click',
        confine: true,
        backgroundColor: '#FFFFFF',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          fontSize: af(12),
          lineHeight: af(16),
        },
        padding: [ah(6), aw(10)],
        extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 9999;',
        formatter: (params: any) => {
          const dataIndex = params.dataIndex
          const dataItem = todayStressData[dataIndex]
          const statusLabel = statusLabels[dataItem.status as keyof typeof statusLabels]
          // 使用状态作为rich样式的key，实现动态颜色
          return `{${dataItem.status}|${statusLabel}} {value|${dataItem.value}%}\n{time| ${dataItem.time}}`
        },
        rich: richStyles
      },
      grid: {
        left: aw(16),
        right: aw(34),
        top: ah(10),
        bottom: ah(30),
        containLabel: false,
      },
      xAxis: {
        type: 'category',
        data: todayStressData.map(item => {
          // 只显示每4小时的标签
          return item.time
        }),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#8489B4',
          fontSize: aw(12),
        },
        splitLine: { show: false },
      },
      yAxis: {
        type: 'value',
        show: true,
        min: 0,
        max: 100,
        position: 'right',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          show: true,
          color: '#8489B4',
          fontSize: aw(12),
          margin: ah(12),
          formatter: function (value: number) {
            if (value === 0 || value === 50 || value === 100) {
              return value.toString();
            }
            return '';
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#BCBFD0',
            type: 'dashed',
          },
        },
      },
      series: [
        // 背景柱状图系列
        {
          type: 'bar',
          data: todayStressData.map((item) => {
            // 判断是否为夜间时段（22:00-06:00）
            const isNightTime = timeUtils.isInTimeRange(item.time, sleepTimeRange)
            return {
              value: 100,
              itemStyle: {
                color: isNightTime ? {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#D6D6F3'
                    },
                    {
                      offset: 0.98,
                      color: 'rgba(255,255,255,0)'
                    }
                  ]
                } : 'transparent', // 非夜间时段使用透明背景
              }
            };
          }),
          barWidth: '100%',
          barCategoryGap: '0%',
          z: 1,
          silent: true,
          animation: false,
        },
        // 主要数据柱子
        {
          type: 'bar',
          data: todayStressData.map(item => ({
            value: item.value,
            itemStyle: {
              color: statusColors[item.status as keyof typeof statusColors],
              borderRadius: [2, 2, 0, 0]
            },
          })),
          barWidth: 2, // 修改为100%让柱子占满空间
          z: 2,
        },
      ],
    }
  }, [todayStressData, sleepTimeRange, theme])

  // 初始化HRV图表
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (hrvChartRef.current && Object.keys(hrvChartOption).length > 0) {
      chartInstance = echarts.init(hrvChartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: 170,
      })
      chartInstance.setOption(hrvChartOption)

      // 添加移动端触摸事件处理
      chartInstance.on('click', (params: any) => {
        // 手动触发tooltip显示
        chartInstance?.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        })
      })
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [hrvChartOption, CHART_WIDTH, CHART_HEIGHT])

  // 初始化压力图表
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (stressChartRef.current && Object.keys(stressChartOption).length > 0) {
      chartInstance = echarts.init(stressChartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: 220,
      })
      chartInstance.setOption(stressChartOption)

      // 添加移动端触摸事件处理
      chartInstance.on('click', (params: any) => {
        // 手动触发tooltip显示
        chartInstance?.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        })
      })
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [stressChartOption, CHART_WIDTH, CHART_HEIGHT, todayStressData, sleepTimeRange])

  // 计算夜间时间段的宽度和偏移量
  const calculateNightTimeBar = () => {
    const cardWidth = screenWidth - aw(64) // 减去左右padding
    // 统计夜间时间段内的数据点数量
    const nightTimeDataPoints = todayStressData.filter((item: any) =>
      timeUtils.isInTimeRange(item.time, sleepTimeRange)
    )
    const totalDataPoints = todayStressData.length

    // 计算夜间数据点占比
    const nightDataRatio = nightTimeDataPoints.length / totalDataPoints
    // 计算第一个夜间数据点的位置作为偏移量
    const firstNightDataIndex = todayStressData.findIndex((item: any) =>
      timeUtils.isInTimeRange(item.time, sleepTimeRange)
    )
    const offsetRatio = (firstNightDataIndex + 1) / totalDataPoints

    // 计算图表实际绘制区域的宽度
    const leftMargin = aw(16) // grid.left
    const rightMargin = aw(34) // grid.right
    const chartWidth = cardWidth - leftMargin - rightMargin
    // 计算实际的宽度和偏移量
    const widthVal = nightDataRatio * chartWidth
    const leftVal = offsetRatio * chartWidth + aw(6)
    return {
      width: widthVal,
      left: sleepTimeRange[0] !== "00:00" ? leftVal : aw(15),
    }
  }

  const $styles = createStyles(theme)

  // 渲染夜间时间段标识条
  const renderNightTimeBar = () => (
    <View style={$styles.nightTimeBarContainer}>
      <View style={[
        $styles.nightTimeBar,
        {
          width: calculateNightTimeBar().width,
          left: calculateNightTimeBar().left
        }
      ]}>
        <View style={$styles.nightTimeIcon}>
          <Icon icon="bed" size={aw(14)} color={'#9896F1'} />
        </View>
      </View>
    </View>
  )

  // 图例
  const renderLegend = () => (
    <View style={$styles.legendContainer}>
      {Object.entries(statusLabels).map(([key, label]) => (
        <View key={key} style={$styles.legendItem}>
          <View
            style={[
              $styles.legendDot,
              { backgroundColor: statusColors[key as keyof typeof statusColors] }
            ]}
          />
          <Text style={$styles.legendText}>{label}</Text>
        </View>
      ))}
    </View>
  )

  return (
    <View style={$styles.card}>
      {/* HRV趋势部分 */}
      <View style={$styles.sectionContainer}>
        <Text style={[themedStyles.sectionTitle, { marginBottom: ah(10), marginLeft: aw(4) }]}>今日HRV趋势</Text>
        <View style={$styles.chartContainer}>
          <SvgChart ref={hrvChartRef} />
        </View>
        {/* 解读 */}
      </View>

      {/* 实时压力部分 */}
      <View>
        <Text style={[themedStyles.sectionTitle, { marginBottom: ah(10), marginLeft: aw(4) }]}>实时压力</Text>
        <View style={$styles.chartContainer}>
          {todayStressData.length > 0 && renderNightTimeBar()}
          <SvgChart ref={stressChartRef} />
        </View>
        {todayStressData.length > 0 && renderLegend()}
      </View>
    </View>
  )
}

const createStyles = (theme: ReturnType<typeof useAppTheme>) => ({
  card: {
    ...globalStyles.card,
    marginBottom: aw(theme.theme.spacing.md),
    paddingVertical: ah(24),
    paddingHorizontal: aw(theme.theme.spacing.md),
  } as ViewStyle,

  sectionContainer: {
    marginBottom: ah(theme.theme.spacing.lg),
  } as ViewStyle,

  chartContainer: {
    width: '100%',
  } as ViewStyle,

  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: aw(theme.theme.spacing.sm),
    paddingHorizontal: aw(theme.theme.spacing.xs),
  } as ViewStyle,

  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  legendDot: {
    width: aw(8),
    height: aw(8),
    borderRadius: aw(4),
    marginRight: aw(6),
  } as ViewStyle,

  legendText: {
    fontSize: 12,
    color: theme.theme.colors.palette.primary200,
    lineHeight: 16,
    fontFamily: typography.primary.bold,
  } as TextStyle,

  divider: {
    height: 1,
    backgroundColor: theme.theme.colors.border,
    marginVertical: aw(theme.theme.spacing.md),
    marginHorizontal: aw(4),
  } as ViewStyle,

  // 夜间时间段相关样式
  nightTimeBarContainer: {
    width: '100%',
    height: aw(20),
    marginBottom: ah(-10),
    position: 'relative',
  } as ViewStyle,

  nightTimeBar: {
    position: 'absolute',
    height: '100%',
    backgroundColor: 'white',
    borderBottomWidth: 2,
    borderBottomColor: '#9896F1',
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  nightTimeIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  nightTimeIconText: {
    fontSize: af(12),
  } as TextStyle,
})