import { FC } from "react"
import { View, ViewStyle, ImageStyle, TextStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text, Icon } from "@/components"
import { HistoryRecord } from "@/data/types"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, ah } from "@/utils/adaptiveSize"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"
import { IconTypes } from "@/components/Icon"
import { typography } from "@/theme/typography"
interface HistoryCardProps {
  historyRecords: HistoryRecord[]
}

export const HistoryCard: FC<HistoryCardProps> = observer(function HistoryCard({
  historyRecords,
}) {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)
  const { $styles, getStatusColor } = createStyles(theme)

  const getStatusIcon = (status: string): IconTypes => {
    switch (status) {
      case "excellent":
        return "statusExcellent"
      case "normal":
        return "statusNormal"
      case "attention":
        return "stressWarning"
      case "overload":
        return "stressOverload"
      default:
        return "statusNormal"
    }
  }

  return (
    <View >
      <Text style={[themedStyles.sectionTitle, { marginTop: ah(8) }]} preset="subheading" text="历史记录" />

      <View style={$styles.container}>
        {historyRecords.map((record, index) => (
          <View
            key={record.id}
            style={[
              $styles.recordItem,
              index === historyRecords.length - 1 && $styles.lastRecordItem,
            ]}
          >
            <View style={$styles.contentContainer}>
              <View style={$styles.leftContent}>
                <View style={$styles.iconContainer}>
                  <Icon
                    style={$styles.icon}
                    icon={getStatusIcon(record.status)}
                    size={aw(12)}
                    color={getStatusColor(record.status)}
                  />
                  <Text style={$styles.statusText} text={record.statusText} />
                </View>
                <Text style={$styles.timeText} text={record.time} />
              </View>
              <Text style={$styles.responseTimeText} text={`${record.responseTime}ms`} />
            </View>
          </View>
        ))}
      </View>
    </View>

  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  const getStatusColor = (status: string): string => {
    switch (status) {
      case "excellent":
        return "#11D3BC" // 绿色
      case "normal":
        return "#9896F1" // 蓝色
      case "attention":
        return "#FFAA64" // 橙色
      case "overload":
        return "#F85F73" // 红色
      default:
        return theme.theme.colors.text
    }
  }

  const $styles = {
    container: {
      ...globalStyles.card,
      marginTop: ah(theme.theme.spacing.xs),
    } as ViewStyle,
    titleContainer: {
      marginBottom: aw(theme.theme.spacing.md),
    } as ViewStyle,
    recordItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: aw(theme.theme.spacing.xs),
    } as ViewStyle,
    lastRecordItem: {
      borderBottomWidth: 0,
    } as ViewStyle,
    iconContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "baseline",
    } as ViewStyle,
    icon: {
      width: aw(12),
      height: ah(12),
      marginRight: aw(8),
    } as ImageStyle,
    statusText: {
      width: aw(100),
      fontSize: aw(16),
      lineHeight: ah(18),
      color: theme.theme.colors.text,
      fontFamily: typography.primary.bold,
      marginBottom: ah(2),
    } as TextStyle,
    contentContainer: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    } as ViewStyle,
    leftContent: {
      flex: 1,
    } as ViewStyle,
    timeText: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary200,
    } as TextStyle,
    responseTimeText: {
      fontSize: aw(14),
      fontFamily: typography.primary.bold,
      color: theme.theme.colors.palette.primary200,
    } as TextStyle,
  }

  return { $styles, getStatusColor }
}