import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Screen, Text, Header } from "@/components"
import { StressTrendCard } from "./components/StressTrendCard"
import { HistoryCard } from "./components/HistoryCard"
import { mockData } from "@/data/stressData"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { aw, ah } from "@/utils/adaptiveSize"
import { $styles as globalStyles } from "@/theme/styles"
import { typography } from "@/theme"
interface StressDayScreenProps extends AppStackScreenProps<"StressDayScreen"> { }

export const StressDayScreen: FC<StressDayScreenProps> = observer(function StressDayScreen(_props) {
  const theme = useAppTheme()

  const $styles = {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    bottomContainerInsets: {
      ...useSafeAreaInsetsStyle(["bottom"]),
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,

    metricsRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: aw(theme.theme.spacing.md),
    } as ViewStyle,

    metricCard: {
      ...globalStyles.card,
      flex: 1,

    } as ViewStyle,

    metricTitle: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary200,
      marginBottom: ah(theme.theme.spacing.xs),
    } as ViewStyle,

    metricValue: {
      fontSize: aw(24),
      fontFamily: typography.primary.bold,
      color: theme.theme.colors.text,
    } as ViewStyle,

    metricUnit: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary100,
    } as ViewStyle,

    metricTime: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
      marginTop: aw(theme.theme.spacing.xs),
    } as ViewStyle,

    metricValueRow: {
      flexDirection: "row",
      alignItems: "baseline",
    } as ViewStyle,
  }

  return (
    <>
      <Header
        title="今日压力详情"
        leftIcon="caretLeft"
        onLeftPress={() => _props.navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />

      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >

        <View style={$styles.container}>
          <StressTrendCard
            todayHrvData={mockData.todayHrvData}
            todayStressData={mockData.todayStressData}
            sleepTimeRange={['00:00', '07:00']}
          />
          {/* 指标卡片 */}
          <View>
            <View style={$styles.metricsRow}>
              <View style={$styles.metricCard}>
                <Text style={$styles.metricTitle}>今日平均HRV</Text>
                <View style={$styles.metricValueRow}>
                  <Text size="xl" style={$styles.metricValue}>{mockData.todayMetrics.dailyAverageHrv.value}</Text>
                  <Text style={$styles.metricUnit}> {mockData.todayMetrics.dailyAverageHrv.unit}</Text>
                </View>
              </View>

              <View style={[$styles.metricCard, { marginLeft: aw(9) }]}>
                <Text style={$styles.metricTitle}>今日平均压力</Text>
                <View style={$styles.metricValueRow}>
                  <Text size="xl" style={$styles.metricValue}>{mockData.todayMetrics.dailyAverageStress.value}</Text>
                  <Text style={$styles.metricUnit}> {mockData.todayMetrics.dailyAverageStress.unit}</Text>
                </View>
              </View>
            </View>

            <View style={$styles.metricsRow}>
              <View style={$styles.metricCard}>
                <Text style={$styles.metricTitle}>状态最优</Text>
                <View style={$styles.metricValueRow}>
                  <Text size="xl" style={$styles.metricValue}>{mockData.todayMetrics.bestStatus.value}</Text>
                  <Text style={$styles.metricUnit}> {mockData.todayMetrics.bestStatus.unit}</Text>
                </View>
                <Text style={$styles.metricTime}>{mockData.todayMetrics.bestStatus.time}</Text>
              </View>

              <View style={[$styles.metricCard, { marginLeft: aw(9) }]}>
                <Text style={$styles.metricTitle}>压力最大</Text>
                <View style={$styles.metricValueRow}>
                  <Text size="xl" style={$styles.metricValue}>{mockData.todayMetrics.maxStress.value}</Text>
                  <Text style={$styles.metricUnit}> {mockData.todayMetrics.maxStress.unit}</Text>
                </View>
                <Text style={$styles.metricTime}>{mockData.todayMetrics.maxStress.time}</Text>
              </View>
            </View>
          </View>
          {/* 历史记录 */}
          <HistoryCard historyRecords={mockData.historyRecords} />

        </View>
      </Screen>
    </>
  )
})
