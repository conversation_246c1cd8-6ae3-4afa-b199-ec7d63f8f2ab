import { observer } from "mobx-react-lite"
import React, { FC } from "react"
import { Image, ImageStyle, TextStyle, View, ViewStyle, Alert } from "react-native"
import { useStores } from "@/models"
import { Button, Text, Screen } from "@/components"
import { AppStackScreenProps } from "../navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { Linking } from "react-native"
import { colors, spacing } from "@/theme"
import { aw } from "@/utils/adaptiveSize"
import { useMemo } from "react"
import useHealthData from "@/hooks/useHealthData"

interface WelcomeScreenProps extends AppStackScreenProps<"Welcome"> { }

export const WelcomeScreen: FC<WelcomeScreenProps> = observer(function WelcomeScreen(_props) {
  const { navigation } = _props
  const theme = useAppTheme()
  const { authenticationStore } = useStores()
  const { initializeHealthKit } = useHealthData()

  async function goNext() {
    const { success, error } = await initializeHealthKit()

    if (success) {
      authenticationStore.setHasSeenWelcome(true)
      navigation.navigate("StressNavigator")
    } else {
      Alert.alert("权限请求", "我们需要您的健康数据权限来提供核心功能，请在设置中开启。", [
        { text: "取消", style: "cancel" },
        { text: "去设置", onPress: () => Linking.openSettings() },
      ])
      console.error("HealthKit initialization failed:", error)
    }
  }

  const $styles = useMemo(() => createStyles(theme), [theme])
  return (
    <>
      <Screen
        preset="auto"
        contentContainerStyle={$styles.screen}
        safeAreaEdges={["bottom"]}
      >
        <View style={$styles.container}>
          <View style={$styles.header}>
            <View style={$styles.logoContainer}>
              <Image
                source={require("assets/images/calm-img.png")}
                style={$styles.logo}
                resizeMode="contain"
              />
            </View>
            <Text style={$styles.title}>CalmWatch</Text>
            <Text style={$styles.subtitle}>感知身体压力，开启健康之旅</Text>
          </View>

          <View style={$styles.buttonContainer}>
            <Button
              preset="default"
              style={$styles.appleButton}
              pressedStyle={$styles.appleButtonPressed}
              onPress={goNext}
            >
              <Text style={$styles.appleButtonText}>
                开始
              </Text>
            </Button>
            <View style={$styles.footer}>
              <Text style={$styles.footerText}>
                点击按钮表示您同意我们的
                <Text style={$styles.linkText} onPress={() => Linking.openURL('https://wathch.zoss.cc/privacyPolicy')}>
                  隐私协议
                </Text>
                和
                <Text style={$styles.linkText} onPress={() => Linking.openURL('https://wathch.zoss.cc/termsService')}>
                  服务条款
                </Text>
              </Text>
            </View>
          </View>
        </View>
      </Screen>
    </>

  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screen: {
      flex: 1,
      backgroundColor: theme.theme.colors.background,
      paddingHorizontal: spacing.xl,
      justifyContent: 'space-between',
      paddingVertical: aw(42),
    } as ViewStyle,
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
    } as ViewStyle,
    header: {
      alignItems: 'center',
      marginTop: aw(123),
    } as ViewStyle,
    logoContainer: {
      position: 'relative',
      marginBottom: spacing.xl,
    } as ViewStyle,
    logo: {
      width: aw(184),
      height: aw(184),
    } as ImageStyle,
    starDecoration: {
      position: 'absolute',
      width: aw(24),
      height: aw(24),
      tintColor: theme.theme.colors.palette.secondary500,
    } as ImageStyle,
    title: {
      fontSize: aw(36),
      lineHeight: aw(40),
      fontWeight: 'bold',
      color: theme.theme.colors.text,
      fontFamily: 'PingFang SC, PingFang SC',
      marginBottom: aw(8),
      textAlign: 'center',
    } as TextStyle,
    subtitle: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary200,
      textAlign: 'center',
      lineHeight: aw(16),
      maxWidth: '80%',
    } as TextStyle,
    buttonContainer: {
      width: '100%',
      alignItems: 'center',
      marginTop: spacing.xxl,
    } as ViewStyle,
    appleButton: {
      width: '100%',
      height: aw(50),
      borderRadius: aw(32),
      backgroundColor: '#00C785',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    } as ViewStyle,
    appleButtonPressed: {
      backgroundColor: '#00A971', // 比默认颜色深一些的绿色
    } as ViewStyle,
    appleButtonText: {
      color: colors.palette.neutral100,
      fontSize: aw(16),
      fontWeight: '600',
      marginLeft: spacing.sm,
      fontFamily: 'System',
    } as TextStyle,
    footer: {
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
      marginTop: aw(8),
    } as ViewStyle,
    footerText: {
      color: theme.theme.colors.palette.primary200,
      fontSize: aw(12),
      textAlign: 'center',
      lineHeight: aw(18),
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
    } as TextStyle,
    linkText: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary500,
      textDecorationLine: 'underline',
      marginHorizontal: 4,
    } as TextStyle,
  }
}
