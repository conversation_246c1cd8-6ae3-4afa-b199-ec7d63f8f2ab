import React, { useMemo, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { View, ViewStyle, TextStyle, Alert } from 'react-native';
import { Text, Screen, InfoIcon } from '@/components';
import { PressureTrendCard } from './components/PressureTrendCard';
import { useAppTheme } from '@/utils/useAppTheme';
import { aw, ah } from '@/utils/adaptiveSize';
import { LiquidGauge } from '@/components/LiquidGauge/LiquidGauge';
import { MetricCard } from './components/MetricCard';
import { useNavigation } from '@react-navigation/native';
import { mockData } from '@/data/stressData';
import { useStores } from '@/models/helpers/useStores';
import useHealthData from "@/hooks/useHealthData"
import { calculateRestingHRRange } from "@/utils"
import { formatDateToYyyyMmDd } from "@/utils/formatDate"
import { IconTypes } from "@/components/Icon"
import { time } from 'console';

export const HomeScreen = observer(function HomeScreen() {
  // 使用自定义的健康数据 hook
  const {
    fetchHeartRateDay,
    fetchHeartRateLast30Days,
    fetchRestingHeartRateLast30Days,
    fetchBeatToBeatData,
    fetchHeartRateDuringSleep,
    fetchRespiratoryRate,
    fetchBloodOxygen,
    fetchWristTemperature,
    fetchBeatToBeatDataLast30Days,
  } = useHealthData()
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])
  const navigation = useNavigation<any>(); // 临时使用 any 类型避免类型错误
  const [metricsData, setMetricsData] = useState<any[]>([])
  const { healthStore } = useStores()
  const rmssdHRVToday = healthStore.rmssdHRVToday // HRV 趋势
  const heartRateDuringSleep = healthStore.heartRateDuringSleep // 睡眠心率
  const respiratoryRate = healthStore.respiratoryRate // 呼吸率
  const bloodOxygen = healthStore.bloodOxygen // 血氧
  const wristTemperature = healthStore.wristTemperature // 手腕温度
  const restingHeartRateLast30Days = healthStore.restingHeartRateLast30Days // 最近30天静息心率

  // 获取当天HRV 趋势
  const getTodayHrvData = () => {
    // console.log("🚀 ~ HomeScreen ~ rmssdHRVToday:", rmssdHRVToday)
    if (!rmssdHRVToday || !Array.isArray(rmssdHRVToday)) return [];
    const TodayHRV = rmssdHRVToday.map((item: any) => {
      return {
        time: item.startDate,
        value: item.value,
        status: "normal",
      };
    });
    return TodayHRV
  }

  // 计算平均HRV
  const averageHRV = useMemo(() => {
    if (!rmssdHRVToday || rmssdHRVToday.length === 0) return 0;
    const sum = rmssdHRVToday.reduce((acc, curr) => acc + curr.value, 0);
    return Math.round(sum / rmssdHRVToday.length);
  }, [rmssdHRVToday])

  /**
   * 通用的7天指标数据处理函数
   * @param data - 原始数据数组，包含 { value: number, date: string }
   * @param normalRange - 正常范围的元组 [min, max]
   * @param calculateChange - 是否计算与7天平均值的百分比变化
   * @param fullData - (可选) 用于计算周变化的完整数据 (例如14天)
   * @returns 格式化后的、可用于卡片展示的数据对象
   */
  const process7DayMetricData = (
    data: { value: number; date: string }[],
    normalRange: [number, number],
    calculateChange: boolean, // 这个参数现在决定是否计算与上一个7天的变化
  ) => {
    if (!data || data.length === 0) {
      return {
        currentValue: 0,
        eChartData: Array(7).fill({ value: null }),
        normalRange,
        avgLast7Days: 0,
        changeFromLast7Days: 0,
      };
    }

    // Define date ranges to get data from the last 7 and previous 7 days
    const today = new Date();
    const sevenDaysAgo = new Date(new Date().setDate(today.getDate() - 7));
    const fourteenDaysAgo = new Date(new Date().setDate(today.getDate() - 14));

    // Filter data for the two periods based on calendar days
    const last7DaysData = data.filter(d => new Date(d.date) > sevenDaysAgo && new Date(d.date) <= today);
    const previous7DaysData = data.filter(d => new Date(d.date) > fourteenDaysAgo && new Date(d.date) <= sevenDaysAgo);

    // Calculate averages based on the number of days with actual data
    const last7DaysSum = last7DaysData.reduce((sum, item) => sum + item.value, 0);
    const avgLast7Days = last7DaysData.length > 0 ? Math.round(last7DaysSum / last7DaysData.length) : 0;

    const previous7DaysSum = previous7DaysData.reduce((sum, item) => sum + item.value, 0);
    const avgPrevious7Days = previous7DaysData.length > 0 ? Math.round(previous7DaysSum / previous7DaysData.length) : 0;

    // Create a template for the last 7 calendar days for the chart
    const chartDateTemplate = Array.from({ length: 7 }).map((_, i) => {
      const d = new Date();
      d.setDate(d.getDate() - i);
      return formatDateToYyyyMmDd(d);
    }).reverse();

    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const finalChartData = chartDateTemplate.map(date => {
      // Use the correctly filtered last7DaysData to find the data point for the chart
      const dataPoint = last7DaysData.find(d => d.date === date);
      return {
        date,
        time: weekDays[new Date(date).getDay()],
        value: dataPoint ? dataPoint.value : null,
      };
    });

    const currentValue = finalChartData[finalChartData.length - 1]?.value || 0;
    // console.log("🚀 ~ HomeScreen ~ changeFromLast7Days:", ((avgLast7Days - avgPrevious7Days) / avgPrevious7Days) * 100)
    let changeFromLast7Days = 0;
    if (calculateChange && avgPrevious7Days > 0) {
      changeFromLast7Days = Math.round(((avgLast7Days - avgPrevious7Days) / avgPrevious7Days) * 100);
    }

    return { currentValue, eChartData: finalChartData, normalRange, avgLast7Days, changeFromLast7Days };
  }

  // 处理静息心率数据
  const restingHeartRateData = () => {

    if (!restingHeartRateLast30Days || restingHeartRateLast30Days.length === 0) {
      return {
        currentValue: 0,
        eChartData: [],
        normalRange: [60, 100],
        avgLast7Days: 0,
        changeFromLast7Days: 0,
      };
    }

    const normalRange = calculateRestingHRRange(restingHeartRateLast30Days);
    return process7DayMetricData(restingHeartRateLast30Days, normalRange, true); // 静息心率也使用新逻辑
  }

  // 处理睡眠心率数据
  const sleepHeartRateData = () => {
    // 1. 按天对原始睡眠心率数据进行分组和平均
    const dailyAverageSleepHR = Object.values(
      heartRateDuringSleep.reduce<Record<string, { sum: number; count: number; date: string }>>((acc, sample) => {
        const dateKey = formatDateToYyyyMmDd(sample.startDate);
        if (!acc[dateKey]) acc[dateKey] = { sum: 0, count: 0, date: dateKey };
        acc[dateKey].sum += sample.value;
        acc[dateKey].count++;
        return acc;
      }, {})
    ).map(({ sum, count, date }) => ({ value: Math.round(sum / count), date }));

    const normalRange = calculateRestingHRRange(dailyAverageSleepHR);
    return process7DayMetricData(dailyAverageSleepHR, normalRange, true); // 睡眠心率现在也计算周变化
  }

  // 处理呼吸频率率数据
  const respiratoryRateData = () => {
    const normalRange = calculateRestingHRRange(respiratoryRate); // 呼吸率的典型正常范围
    return process7DayMetricData(respiratoryRate, normalRange, true);
  }

  // 处理血氧数据
  const bloodOxygenData = () => {
    const normalRange = calculateRestingHRRange(bloodOxygen); // 血氧的典型正常范围
    return process7DayMetricData(bloodOxygen, normalRange, true);
  }

  // 处理手腕温度数据
  const wristTemperatureData = () => {
    const normalRange = calculateRestingHRRange(wristTemperature); // 手腕温度的典型正常范围
    return process7DayMetricData(wristTemperature, normalRange, true);
  }

  // 初始化指标数据
  const dataInit = () => {
    type MetricsData = {
      currentValue: number
      eChartData: { time: string; value: number | null }[]
      title: string
      unit: string
      icon: IconTypes
      eChartLineColor: string
      normalRange: [number, number]
      avgLast7Days: number
      changeFromLast7Days: number
      /** 指标定义说明文字 */
      metricDefinition?: string
      /** 指标说明标题 */
      metricTitle?: string
      id?: string
    }
    const restingHeartRateCard = {
      ...restingHeartRateData(),
      id: "restingHeartRate",
      title: "静息心率",
      unit: "bpm",
      icon: "restingHeartRate",
      eChartLineColor: "#F85F73",
      metricDefinition: "",
    } as MetricsData
    // 处理睡眠心率数据
    const sleepWeekHeartRateCard = {
      ...sleepHeartRateData(),
      id: "sleepWeekHeartRate",
      title: "睡眠心率",
      unit: "bpm",
      icon: "sleepEfficiency",
      eChartLineColor: "#9896F1",
      metricDefinition: "",
    } as MetricsData
    // 处理呼吸频率率数据
    const respiratoryRateCard = {
      ...respiratoryRateData(),
      id: "breathingRate",
      title: "呼吸频率",
      unit: "br/min",
      icon: "breathingRate",
      eChartLineColor: "#6EB6FF",
      metricDefinition: "",
    } as MetricsData
    // 处理血氧饱和度数据
    const bloodOxygenCard = {
      ...bloodOxygenData(),
      id: "bloodOxygen",
      title: "血氧饱和度",
      unit: "%",
      icon: "bloodOxygen",
      eChartLineColor: "#FFAA64",
      metricDefinition: "",
    } as MetricsData
    // 处理处理手环温度数据
    console.log("🚀 ~ dataInit ~ bloodOxygenCard....手腕温度():", { ...wristTemperatureData() })
    const wristTemperatureCard = {
      ...wristTemperatureData(),
      id: "wristTemperature",
      title: "手腕温度",
      unit: "°C",
      icon: "wristTemperature",
      eChartLineColor: "#FF6B6B",
      metricDefinition: "",
    } as MetricsData
    const metricsData = [restingHeartRateCard, sleepWeekHeartRateCard, respiratoryRateCard, bloodOxygenCard, wristTemperatureCard];
    setMetricsData(metricsData);
    healthStore.setMetricsData(metricsData);
  }

  // 加载健康数据
  const loadHealthData = async () => {
    try {
      healthStore.setLoading(true)
      healthStore.setError(null)

      await Promise.all([
        // fetchHeartRateDay().catch((e: Error) => {
        //   console.warn('获取心率数据失败:', e)
        //   healthStore.setError('获取心率数据失败')
        // }),
        // fetchHeartRateLast30Days().catch((e: Error) => {
        //   console.warn('获取心率数据失败:', e)
        //   healthStore.setError('获取心率数据失败')
        // }),
        fetchRestingHeartRateLast30Days().catch((e: Error) => {
          console.warn('获取静息心率数据失败:', e)
          healthStore.setError('获取静息心率数据失败')
        }),
        fetchBeatToBeatData().catch((e: Error) => {
          console.warn('获取逐搏测量数据失败:', e)
          healthStore.setError('获取逐搏测量数据失败')
        }),
        fetchBeatToBeatDataLast30Days().catch((e: Error) => {
          console.warn('获取30天逐搏测量数据失败:', e)
          healthStore.setError('获取30天逐搏测量数据失败')
        }),
        fetchHeartRateDuringSleep().catch((e: Error) => {
          console.warn('获取睡眠心率数据失败:', e)
          healthStore.setError('获取睡眠心率数据失败')
        }),
        fetchRespiratoryRate().catch((e: Error) => {
          console.warn('获取呼吸率数据失败:', e)
          healthStore.setError('获取呼吸率数据失败')
        }),
        fetchBloodOxygen().catch((e: Error) => {
          console.warn('获取血氧数据失败:', e)
          healthStore.setError('获取血氧数据失败')
        }),
        fetchWristTemperature().catch((e: Error) => {
          console.warn('获取手腕温度数据失败:', e)
          healthStore.setError('获取手腕温度数据失败')
        })
      ])

    } catch (error) {
      console.error('获取健康数据时出错:', error)
      const errorMessage = error instanceof Error ? error.message : '获取健康数据失败'
      console.error('健康数据加载失败:', error)
      Alert.alert('错误', errorMessage)
    } finally {
    }
  }

  // 在组件挂载时获取健康数据
  useEffect(() => {
    loadHealthData().then(() => {
      dataInit();
    })

    // 组件卸载时重置状态
    return () => {
    }
  }, [])

  // 初始化数据
  useEffect(() => {
    dataInit();
  }, [restingHeartRateLast30Days, heartRateDuringSleep, respiratoryRate, bloodOxygen, wristTemperature]); // 当数据变化时重新初始化

  return (
    <Screen
      preset="auto"
      contentContainerStyle={$styles.screenContentContainer}
      safeAreaEdges={["top"]}
    >
      {/* 渐变背景 */}
      <View style={$styles.gradientBackground} />

      {/* 压力水位图 */}
      <View style={$styles.chartContainer}>
        <LiquidGauge
          value={averageHRV}
          unit="ms"
        />
        {/* 状态描述 */}
        <View style={$styles.statusContainer}>
          <Text style={$styles.statusTitle}>状态正常</Text>
          <Text style={$styles.statusDescription}>
            身体各项机能正常，有一定的压力调节能力，建议
          </Text>
          <Text style={$styles.statusDescription}>
            保持规律作息，适度运动，注意劳逸结合
          </Text>
          <View style={$styles.updateInfo}>
            <Text style={$styles.updateText}>14:39数据更新</Text>
            <InfoIcon
              content="数据最后更新时间"
              size={14}
              color="#9CA3AF"
              useOriginalColor
            />
          </View>
        </View>
      </View>

      {/* 压力趋势卡片 */}
      <PressureTrendCard
        todayHrvData={getTodayHrvData()}
        todayStressData={mockData.todayStressData}
        sleepTimeRange={['00:00', '07:00']}
      />

      {/* 核心指标 */}
      <View style={$styles.sectionTitleContainer}>
        <Text style={$styles.sectionTitle}>核心指标</Text>
      </View>

      {metricsData.map((metric) => (
        <MetricCard
          key={metric.id}
          id={metric.id}
          title={metric.title}
          currentValue={metric.currentValue}
          unit={metric.unit}
          status={metric.status}
          chartData={metric.eChartData}
          normalRange={metric.normalRange}
          eChartLineColor={metric.eChartLineColor}
          icon={metric.icon as any}
          onPress={(id) => navigation.navigate('MetricsDetailScreen' as never, { metricId: id })}
        />
      ))}

    </Screen>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,

    gradientBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: 223,
      zIndex: -1,
      backgroundColor: 'transparent',
      backgroundImage: 'linear-gradient(180deg, #D0CFFA 0%, rgba(216,216,216,0) 100%)',
    } as ViewStyle,

    chartContainer: {
      marginTop: 10,
      alignItems: 'center',
      justifyContent: 'center',
    } as ViewStyle,

    statusContainer: {
      alignItems: 'center',
      paddingHorizontal: 20,
    } as ViewStyle,

    statusTitle: {
      fontSize: 24,
      lineHeight: 32,
      fontWeight: 'bold' as const,
      color: theme.theme.colors.palette.primary600,
      marginBottom: 8,
    } as TextStyle,

    statusDescription: {
      fontSize: 12,
      color: theme.theme.colors.palette.primary200,
      fontFamily: theme.theme.typography.primary.bold,
      lineHeight: 16,
    } as TextStyle,

    sectionTitleContainer: {
      marginTop: 24,
      marginBottom: 16,
    } as ViewStyle,

    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      lineHeight: 24,
      color: theme.theme.colors.palette.primary600,
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,

    updateInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
      alignSelf: 'center',
    } as ViewStyle,

    updateText: {
      fontSize: 12,
      color: theme.theme.colors.palette.primary100,
      marginRight: 4,
    } as TextStyle,


  }
}
