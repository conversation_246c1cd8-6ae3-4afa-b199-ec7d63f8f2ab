import React, { useRef, useEffect, useMemo } from 'react';
import { View, ViewStyle, TextStyle, TouchableOpacity } from 'react-native';
import { Text, Icon } from '@/components';
import { useAppTheme } from '@/utils/useAppTheme'
import { aw, ah } from '@/utils/adaptiveSize'
import { $styles as globalStyles } from '@/theme/styles'
import { Dimensions } from 'react-native'
import SvgChart from '@wuba/react-native-echarts/svgChart';
import { IconTypes } from '@/components/Icon'
import type { ECharts } from 'echarts/core';
import { MarkAreaComponent } from 'echarts/components';
import echarts from '@/utils/echarts';

const { width: screenWidth } = Dimensions.get('window');

interface MetricCardProps {
  id?: string;
  title: string;
  currentValue: number;
  unit: string;
  status: 'normal' | 'abnormal';
  chartData: Array<{ value: number | null }> | number[];
  normalRange: [number, number];
  icon: IconTypes;
  eChartLineColor: string;
  onPress?: (id?: string) => void;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  currentValue,
  unit,
  status,
  chartData,
  normalRange,
  icon,
  eChartLineColor,
  onPress,
  id
}) => {

  const chartRef = useRef<any>(null);
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])

  // 计算趋势
  const { trendValue, trend } = useMemo(() => {
    const currentVal = currentValue;
    let trend: 'up' | 'down' | 'none' = 'none'; // 增加 'none' 状态
    let trendValue = 0;

    // 如果今天没有数据，则无法计算趋势
    if (currentVal === 0 || currentVal === null) {
      return { trend: 'none', trendValue: 0 };
    }

    // 只比较倒数第二个元素（昨天）
    if (Array.isArray(chartData) && chartData.length > 1) {
      const yesterdayPoint = chartData[chartData.length - 2] as { value: number | null };
      const yesterdayVal = yesterdayPoint?.value;

      // 如果昨天的数据有效（不是null或0），则计算趋势
      if (yesterdayVal !== null && yesterdayVal !== 0) {
        trendValue = ((currentVal - yesterdayVal) / yesterdayVal) * 100;
        trend = trendValue >= 0 ? 'up' : 'down';
      }
    }

    return {
      trendValue: parseFloat(trendValue.toFixed(1)), // 保留一位小数
      trend
    };
  }, [chartData, currentValue]);

  // 初始化图表
  useEffect(() => {
    let chart: ECharts | undefined;

    // 只有当 normalRange 有效时才初始化图表，避免在 [0,0] 上渲染
    const isRangeValid = normalRange[0] !== 0 || normalRange[1] !== 0;

    if (chartRef.current && isRangeValid) {
      // 必须在使用前注册 markArea 组件
      echarts.use([MarkAreaComponent]);

      chart = echarts.init(chartRef.current, null, {
        width: aw(120),
        height: ah(60),
      });

      const option = {
        tooltip: {
          show: false,
        },
        grid: {
          left: 0,
          right: 0,
          top: '5%',
          bottom: '5%',
          containLabel: false,
        },
        xAxis: {
          type: 'category',
          // data: chartData.map((_, index) => index + 1), // 生成 x 轴数据
          show: false, // 显示 x 轴
        },
        yAxis: {
          type: 'value',
          show: false,
          min: (() => {
            const values = chartData.map(item => (item as { value: number | null }).value).filter((v): v is number => v !== null);
            if (values.length === 0) return 0;
            return Math.min(...values) * 0.9;
          })(),
          max: (() => {
            const values = chartData.map(item => (item as { value: number | null }).value).filter((v): v is number => v !== null);
            if (values.length === 0) return 100;
            return Math.max(...values) * 1.1;
          })()
        },
        series: [
          {
            type: 'line',
            data: Array.isArray(chartData[0]) ? chartData : chartData.map(item => (item as { value: number }).value),
            itemStyle: {
              color: '#FFFFFF',
              borderColor: eChartLineColor,
              borderWidth: aw(2),
            },
            symbol: 'circle',
            symbolSize: aw(8),
            lineStyle: {
              color: eChartLineColor,
              width: aw(1),
              type: 'dashed',
            },
            smooth: true,
            showSymbol: true,
            markArea: {
              silent: true,
              itemStyle: {
                color: eChartLineColor,
                opacity: 0.12,
                borderRadius: [aw(8), aw(8), aw(8), aw(8)], // 上左右圆角，下边直角
              },
              data: [
                [
                  {
                    yAxis: normalRange[0],
                  },
                  {
                    yAxis: normalRange[1],
                  }
                ]
              ]
            }
          },
        ],
      };

      chart.setOption(option);
    }

    return () => {
      // 确保图表实例被销毁
      if (chart && !chart.isDisposed()) {
        chart.dispose();
      }
    };
  }, [chartData, normalRange, eChartLineColor]);

  const handlePress = () => {
    if (onPress) {
      onPress(id);
    }
  };

  return (
    <TouchableOpacity style={$styles.card} onPress={handlePress} activeOpacity={0.7}>
      <View style={$styles.header}>
        <View style={$styles.titleContainer}>
          <Icon icon={icon} size={aw(16)} useOriginalColor />
          <Text style={$styles.title}>{title}</Text>
        </View>
        <Icon icon="caretRight" size={aw(18)} color={theme.theme.colors.palette.primary200} />
      </View>
      <View style={$styles.contentContainer}>
        <View style={$styles.content}>
          <View style={$styles.valueContainer}>
            <Text style={$styles.value}>{currentValue}</Text>
            <Text style={$styles.unit}>/{unit}</Text>
          </View>
          <View style={$styles.footer}>

            {trend !== 'none' ? <View style={$styles.statusBadge}>
              <Icon
                icon={status === 'normal' ? 'indicatorNormal' : 'indicatorError'}
                size={aw(12)}
                useOriginalColor
              />
              <Text style={[
                $styles.statusText,
                status === 'normal' ? $styles.statusTextNormal : $styles.statusTextError
              ]}>
                {status === 'normal' ? '正常' : '异常'}
              </Text>
            </View> : null}

            <View style={$styles.trendContainer}>
              {trend !== 'none' ? (
                <Text style={$styles.trendText}>
                  较昨日{trend === 'up' ? '上升' : '下降'} {Math.abs(trendValue)}%
                </Text>
              ) : (
                <Text style={$styles.trendText}>暂无数据</Text>
              )}
            </View>
          </View>

        </View>

        <View >
          <SvgChart key={id} ref={chartRef} />
        </View>
      </View>

    </TouchableOpacity>
  );
};


const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    card: {
      ...globalStyles.card,
      width: screenWidth - aw(32),
      alignSelf: 'center',
      padding: aw(16),
      marginBottom: ah(16),
    } as ViewStyle,
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    } as ViewStyle,
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    caretIcon: {
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    title: {
      fontSize: aw(14),
      lineHeight: aw(18),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
      marginLeft: aw(4),
    } as TextStyle,
    contentContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    } as ViewStyle,
    content: {
      marginTop: ah(16),
    } as ViewStyle,
    valueContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    value: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
    } as TextStyle,
    unit: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary100,
      marginLeft: 4,
    } as TextStyle,
    footer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: ah(4),
      borderRadius: aw(8),
    } as ViewStyle,
    statusText: {
      fontSize: aw(12),
      lineHeight: aw(16),
      marginLeft: aw(2),
    } as TextStyle,
    statusTextNormal: {
      color: '#6892D5',
    } as TextStyle,
    statusTextError: {
      color: '#E84A5F',
    } as TextStyle,
    trendContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    trendText: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
      marginLeft: aw(8),
    } as TextStyle,
  };
}
