import { observer } from "mobx-react-lite"
import { useState, useMemo, useRef, useEffect } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle, Dimensions } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { Text, Icon } from "@/components"
import { $styles as globalStyles } from "@/theme/styles"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"
import { af, ah, aw } from "@/utils/adaptiveSize"
import echarts from "@/utils/echarts"
import { timeUtils } from "@/utils/timeUtils"
import { useNavigation } from "@react-navigation/native"
import { format, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const { width: screenWidth } = Dimensions.get('window')


// 状态颜色映射
const statusColors = {
  excellent: '#11D3BC', // 状态优秀 - 青色
  normal: '#9896F1',    // 状态正常 - 深蓝色
  attention: '#FFAA64', // 注意压力 - 中蓝色
  overload: '#F85F73',  // 压力过载 - 红色
}

// 状态标签映射
const statusLabels = {
  excellent: '状态优秀',
  normal: '状态正常',
  attention: '注意压力',
  overload: '压力过载',
}

interface StressTrendCardProps {
  todayHrvData: HrvDataPoint[]
  todayStressData: StressDataPoint[]
  sleepTimeRange: [string, string]
}


/**
 * 压力趋势卡片组件
 * 用于展示压力趋势图表
 */
export const PressureTrendCard = observer(({ todayHrvData, todayStressData, sleepTimeRange }: StressTrendCardProps) => {
  const theme = useAppTheme()
  const [activeTab, setActiveTab] = useState<'hrv' | 'stress'>('hrv')
  const hrvChartRef = useRef<any>(null)
  const stressChartRef = useRef<any>(null)
  const CHART_WIDTH = screenWidth - 64 // 减去左右padding

  // 获取当天的 00:00 和 24:00 时间
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const todayEnd = new Date(today)
  todayEnd.setHours(24, 0, 0, 0) // 设置为今天的 24:00
  const CHART_HEIGHT = 160
  const $styles = useMemo(() => createStyles(theme), [theme])

  const navigation = useNavigation<any>()

  // HRV趋势图配置
  const hrvChartOption = useMemo(() => {
    // 为每种状态创建动态的rich样式
    const richStyles: any = {
      value: {
        fontSize: af(12),
        color: '#333',
        fontWeight: '600'
      },
      time: {
        fontSize: af(11),
        color: '#666',
        lineHeight: af(18)
      }
    }

    // 为每种状态添加对应颜色的样式
    Object.keys(statusColors).forEach(status => {
      richStyles[status] = {
        fontSize: af(12),
        fontWeight: 'bold',
        color: statusColors[status as keyof typeof statusColors]
      }
    })

    return {
      tooltip: {
        trigger: 'item',
        triggerOn: 'click',
        confine: true,
        backgroundColor: '#FFFFFF',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          fontSize: af(12),
          lineHeight: af(16),
        },
        padding: [ah(6), aw(10)],
        extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 9999;',
        formatter: (params: any) => {
          const dataIndex = params.dataIndex
          const dataItem = todayHrvData[dataIndex]
          const statusLabel = statusLabels[dataItem.status as keyof typeof statusLabels]

          // 使用状态作为rich样式的key，实现动态颜色
          return `{${dataItem.status}|${statusLabel}} {value|${dataItem.value}ms}\n{time|HRV · ${format(parseISO(dataItem.time), 'HH:mm', { locale: zhCN })}}`
        },
        rich: richStyles
      },
      grid: {
        left: aw(20),
        right: aw(20),
        top: ah(6),
        bottom: ah(30),
        containLabel: false,
      },
      xAxis: {
        type: 'time',
        min: today.getTime(),
        max: todayEnd.getTime(),
        splitNumber: 3,
        boundaryGap: false,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#8489B4',
          fontSize: aw(12),
          margin: ah(12),
          align: 'center',
          showMaxLabel: true,
          formatter: function (value: number) {
            try {
              // 对于时间类型，value 是时间戳
              const date = typeof value === 'number' ? new Date(value) : parseISO(value);
              // 检查是否是当天的最后一个时间点（24:00）
              if (date.getHours() === 0 && date.getMinutes() === 0 &&
                date.getTime() === todayEnd.getTime()) {
                return '24:00';
              }
              return format(date, 'HH:mm', { locale: zhCN });
            } catch (error) {
              console.warn('Failed to format time:', error);
              return String(value);
            }
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#BCBFD0',
            type: 'dashed',
          },
        },
      },
      yAxis: {
        type: 'value',
        show: false,
        min: (() => {
          const minValue = Math.min(...todayHrvData.map(item => item.value));
          return Math.max(0, Math.floor(minValue / 10) * 10 - 10); // 向下取整到最近的10的倍数，并减去10作为缓冲
        })(),
        max: (() => {
          const maxValue = Math.max(...todayHrvData.map(item => item.value));
          return Math.ceil(maxValue / 10) * 10 + 10; // 向上取整到最近的10的倍数，并加上10作为缓冲
        })(),
      },
      series: [
        {
          type: 'line',
          data: todayHrvData
            .map(item => ({
              value: [item.time, item.value],
              itemStyle: {
                color: '#FFFFFF',
                borderColor: statusColors[item.status as keyof typeof statusColors],
                borderWidth: aw(2),
              },
              symbol: 'circle',
              symbolSize: aw(12),
            }))
            .sort((a, b) => new Date(a.value[0]).getTime() - new Date(b.value[0]).getTime()),
          lineStyle: {
            color: '#8489B4',
            width: aw(1),
          },
          smooth: false,
          showSymbol: true,
        },
      ],
    }
  }, [todayHrvData])

  // 初始化HRV图表
  useEffect(() => {
    let chartInstance: any
    if (hrvChartRef.current && Object.keys(hrvChartOption).length > 0) {
      chartInstance = echarts.init(hrvChartRef.current, null, {
        width: CHART_WIDTH,
        height: CHART_HEIGHT,
      })
      chartInstance.setOption(hrvChartOption)

      // 添加移动端触摸事件处理
      chartInstance.on('click', (params: any) => {
        // 手动触发tooltip显示
        chartInstance?.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        })
      })
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [hrvChartOption, CHART_WIDTH, CHART_HEIGHT, todayHrvData, activeTab])


  // 实时压力柱状图配置
  const stressChartOption = useMemo(() => {
    // 为每种状态创建动态的rich样式
    const richStyles: any = {
      value: {
        fontSize: af(12),
        color: '#333',
        fontWeight: '600'
      },
      time: {
        fontSize: af(11),
        color: '#666',
        lineHeight: af(18)
      }
    }

    // 为每种状态添加对应颜色的样式
    Object.keys(statusColors).forEach(status => {
      richStyles[status] = {
        fontSize: af(12),
        fontWeight: 'bold',
        color: statusColors[status as keyof typeof statusColors]
      }
    })

    return {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        triggerOn: 'click',
        confine: true,
        backgroundColor: '#FFFFFF',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          fontSize: af(12),
          lineHeight: af(16),
        },
        padding: [ah(6), aw(10)],
        extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 9999;',
        formatter: (params: any) => {
          const dataIndex = params.dataIndex
          const dataItem = todayStressData[dataIndex]
          const statusLabel = statusLabels[dataItem.status as keyof typeof statusLabels]
          // 使用状态作为rich样式的key，实现动态颜色
          return `{${dataItem.status}|${statusLabel}} {value|${dataItem.value}%}\n{time| ${dataItem.time}}`
        },
        rich: richStyles
      },
      grid: {
        left: aw(16),
        right: aw(34),
        top: ah(10),
        bottom: ah(30),
        containLabel: false,
      },
      xAxis: {
        type: 'category',
        data: todayStressData.map(item => {
          // 只显示每4小时的标签
          return item.time
        }),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#8489B4',
          fontSize: aw(12),
        },
        splitLine: { show: false },
      },
      yAxis: {
        type: 'value',
        show: true,
        min: 0,
        max: 100,
        position: 'right',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          show: true,
          color: '#8489B4',
          fontSize: aw(12),
          margin: ah(12),
          formatter: function (value: number) {
            if (value === 0 || value === 50 || value === 100) {
              return value.toString();
            }
            return '';
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#BCBFD0',
            type: 'dashed',
          },
        },
      },
      series: [
        // 背景柱状图系列
        {
          type: 'bar',
          data: todayStressData.map((item) => {
            // 判断是否为夜间时段（22:00-06:00）
            const isNightTime = timeUtils.isInTimeRange(item.time, sleepTimeRange)
            return {
              value: 100,
              itemStyle: {
                color: isNightTime ? {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#D6D6F3'
                    },
                    {
                      offset: 0.98,
                      color: 'rgba(255,255,255,0)'
                    }
                  ]
                } : 'transparent', // 非夜间时段使用透明背景
              }
            };
          }),
          barWidth: '100%',
          barCategoryGap: '0%',
          z: 1,
          silent: true,
          animation: false,
        },
        // 主要数据柱子
        {
          type: 'bar',
          data: todayStressData.map(item => ({
            value: item.value,
            itemStyle: {
              color: statusColors[item.status as keyof typeof statusColors],
              borderRadius: [2, 2, 0, 0]
            },
          })),
          barWidth: 2, // 修改为100%让柱子占满空间
          z: 2,
        },
      ],
    }
  }, [todayStressData, sleepTimeRange, theme])

  // 初始化压力图表
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (stressChartRef.current && Object.keys(stressChartOption).length > 0) {
      chartInstance = echarts.init(stressChartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: 220,
      })
      chartInstance.setOption(stressChartOption)

      // 添加移动端触摸事件处理
      chartInstance.on('click', (params: any) => {
        // 手动触发tooltip显示
        chartInstance?.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        })
      })
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [stressChartOption, CHART_WIDTH, CHART_HEIGHT, todayStressData, sleepTimeRange])

  // 计算夜间时间段的宽度和偏移量
  const calculateNightTimeBar = () => {
    const cardWidth = screenWidth - aw(64) // 减去左右padding
    // 统计夜间时间段内的数据点数量
    const nightTimeDataPoints = todayStressData.filter((item: any) =>
      timeUtils.isInTimeRange(item.time, sleepTimeRange)
    )
    const totalDataPoints = todayStressData.length

    // 计算夜间数据点占比
    const nightDataRatio = nightTimeDataPoints.length / totalDataPoints
    // 计算第一个夜间数据点的位置作为偏移量
    const firstNightDataIndex = todayStressData.findIndex((item: any) =>
      timeUtils.isInTimeRange(item.time, sleepTimeRange)
    )
    const offsetRatio = (firstNightDataIndex + 1) / totalDataPoints

    // 计算图表实际绘制区域的宽度
    const leftMargin = aw(16) // grid.left
    const rightMargin = aw(34) // grid.right
    const chartWidth = cardWidth - leftMargin - rightMargin
    // 计算实际的宽度和偏移量
    const widthVal = nightDataRatio * chartWidth
    const leftVal = offsetRatio * chartWidth + aw(6)
    return {
      width: widthVal,
      left: sleepTimeRange[0] !== "00:00" ? leftVal : aw(15),
    }
  }


  // 渲染夜间时间段标识条
  const renderNightTimeBar = () => (
    <View style={$styles.nightTimeBarContainer}>
      <View style={[
        $styles.nightTimeBar,
        {
          width: calculateNightTimeBar().width,
          left: calculateNightTimeBar().left
        }
      ]}>
        <View style={$styles.nightTimeIcon}>
          <Icon icon="bed" size={aw(14)} color={'#9896F1'} />
        </View>
      </View>
    </View>
  )

  return (
    <View style={$styles.container}>
      <View style={$styles.tabContainer}>
        <TouchableOpacity
          style={[$styles.tabButton, activeTab === 'hrv' && $styles.activeTabButton]}
          onPress={() => setActiveTab('hrv')}
        >
          <Text
            style={[$styles.tabText, activeTab === 'hrv' ? $styles.activeTabText : $styles.inactiveTabText]}
          >
            今日HRV趋势
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[$styles.tabButton, activeTab === 'stress' && $styles.activeTabButton]}
          onPress={() => setActiveTab('stress')}
        >
          <Text
            style={[$styles.tabText, activeTab === 'stress' ? $styles.activeTabText : $styles.inactiveTabText]}
          >
            实时压力
          </Text>
        </TouchableOpacity>
      </View>

      {/* HRV趋势内容 */}
      {activeTab === 'hrv' && (
        <View>
          <View style={$styles.chartContainer}>
            <SvgChart ref={hrvChartRef} />
          </View>

          {/* 解读 */}
          <View style={$styles.analysisContainer}>
            <Text style={$styles.analysisTitle}>
              趋势分析
              <Text style={$styles.sparkleIcon}>✨</Text>
            </Text>
            <View style={$styles.analysisContent}>
              <Text style={$styles.analysisText}>
                此刻你的 HRV 心率变异性处于正常水平，代表此刻你的状态正常，身心压力水平可控。相信此刻的你足够应对日常工作、学习、运动等挑战。但也要注意不要过于挑战自己，记得张弛有度。
              </Text>
            </View>
          </View>
        </View>
      )
      }

      {/* 实时压力内容 */}
      {activeTab === 'stress' && (
        <View>
          <View style={$styles.chartContainer}>
            {todayStressData.length > 0 && renderNightTimeBar()}
            <SvgChart ref={stressChartRef} />
          </View>
        </View>
      )
      }

      {/* 详细解读按钮 */}
      <TouchableOpacity
        style={$styles.analysisButton}
        onPress={() => navigation.navigate('StressDayScreen')}
      >
        <Text style={$styles.analysisButtonText}>详细解读</Text>
      </TouchableOpacity>
    </View >
  )
})


const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    container: {
      ...globalStyles.card,
      marginTop: 16,
    } as ViewStyle,

    tabContainer: {
      flexDirection: 'row',
      borderRadius: 20,
      padding: 4,
      marginBottom: 16,
    } as ViewStyle,

    tabButton: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: 8,
      borderRadius: 16,
    } as ViewStyle,

    activeTabButton: {
      backgroundColor: theme.theme.colors.palette.neutral200,
    } as ViewStyle,

    tabText: {
      fontSize: 16,
      fontFamily: theme.theme.typography.primary.bold,
      lineHeight: 24,
    } as TextStyle,

    activeTabText: {
      color: theme.theme.colors.palette.primary301,
    } as TextStyle,

    inactiveTabText: {
      color: theme.theme.colors.palette.neutral600,
    } as TextStyle,

    title: {
      fontSize: 16,
      fontWeight: "600" as const,
      color: theme.theme.colors.palette.primary600,
      marginBottom: 16,
    },

    content: {
      minHeight: 200,
      justifyContent: "center",
      alignItems: "center",
    } as ViewStyle,

    chartContainer: {
      width: '100%',
      marginTop: -16,
      marginBottom: 16,
    } as ViewStyle,

    // 夜间时间段相关样式
    nightTimeBarContainer: {
      width: '100%',
      height: aw(20),
      marginBottom: ah(-10),
      position: 'relative',
    } as ViewStyle,

    nightTimeBar: {
      position: 'absolute',
      height: '100%',
      backgroundColor: 'white',
      borderBottomWidth: 2,
      borderBottomColor: '#9896F1',
      justifyContent: 'center',
      alignItems: 'center',
    } as ViewStyle,

    nightTimeIcon: {
      justifyContent: 'center',
      alignItems: 'center',
    } as ViewStyle,

    nightTimeIconText: {
      fontSize: af(12),
    } as TextStyle,

    // 解读部分样式
    analysisContainer: {
    } as ViewStyle,

    analysisTitle: {
      fontSize: aw(16),
      fontWeight: '600',
      lineHeight: aw(24),
      color: theme.theme.colors.palette.primary600,
      flexDirection: 'row',
      alignItems: 'center',
    } as TextStyle,

    sparkleIcon: {
      marginLeft: 4,
      marginTop: aw(-16),
    } as TextStyle,

    analysisContent: {
      marginTop: aw(8),
      backgroundColor: theme.theme.colors.palette.neutral200,
      borderRadius: aw(12),
      padding: aw(16),
    } as ViewStyle,

    analysisText: {
      fontSize: aw(14),
      lineHeight: aw(24),
      color: theme.theme.colors.palette.primary200,
      fontFamily: theme.theme.typography.primary.bold,
      textAlign: 'left',
    } as TextStyle,

    analysisButton: {
      marginTop: aw(16),
      borderRadius: aw(12),
    } as ViewStyle,

    analysisButtonText: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: '#699AE5',
      fontFamily: theme.theme.typography.primary.bold,
      textAlign: 'center',
    } as TextStyle,
  }
}
