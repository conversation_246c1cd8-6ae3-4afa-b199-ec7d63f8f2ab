import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, Text } from "react-native"
import { observer } from "mobx-react-lite"
import { Icon, type IconTypes } from "@/components/Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw } from "@/utils/adaptiveSize"

type MenuItemProps = {
  icon: IconTypes
  text: string
  onPress?: () => void
  showArrow?: boolean
  style?: ViewStyle
}

export const MenuItem = observer(({
  icon,
  text,
  onPress,
  showArrow = true,
  style
}: MenuItemProps) => {
  const theme = useAppTheme()

  return (
    <TouchableOpacity
      style={[{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: aw(16),
        paddingHorizontal: aw(16),
      }, style]}
      activeOpacity={0.7}
      onPress={onPress}
    >
      <View style={{
        width: aw(24),
        height: aw(24),
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: aw(12),
      }}>
        <Icon icon={icon} size={aw(20)} color={theme.theme.colors.palette.primary600} />
      </View>

      <Text style={{
        flex: 1,
        fontSize: aw(16),
        color: theme.theme.colors.palette.primary600,
        fontFamily: theme.theme.typography.primary.medium,
      }}>
        {text}
      </Text>

      {showArrow && (
        <Icon
          icon="caretRight"
          size={aw(18)}
          color={theme.theme.colors.palette.primary200}
        />
      )}
    </TouchableOpacity>
  )
})
