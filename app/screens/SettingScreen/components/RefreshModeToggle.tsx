import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, TouchableOpacity, Text } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw } from "@/utils/adaptiveSize"
import { useState } from "react"
import { $styles as globalStyles } from "@/theme/styles"
import { Icon } from "@/components/Icon"

export const RefreshModeToggle = observer(() => {
  const theme = useAppTheme()
  const [isHrvMode, setIsHrvMode] = useState(false)
  const $styles = createStyles(theme)

  return (
    <View style={$styles.menuItem}>
      <View style={$styles.menuIconContainer}>
        <Icon icon="hrvRefreshMode" size={aw(20)} color={theme.theme.colors.palette.primary600} />
      </View>
      <Text style={$styles.menuText}>HRV刷新模式</Text>
      <TouchableOpacity
        style={$styles.toggleContainer}
        activeOpacity={0.8}
        onPress={() => setIsHrvMode(!isHrvMode)}
      >
        <View style={$styles.toggleTrack}>
          <View style={$styles.toggleTextContainer}>
            <Text style={[
              $styles.toggleText1,
              isHrvMode && $styles.toggleTextActive
            ]}>HRV</Text>
            <Text style={[
              $styles.toggleText2,
              !isHrvMode && $styles.toggleTextActive
            ]}>实时压力</Text>
          </View>
          <View style={[
            $styles.toggleThumb,
            isHrvMode && $styles.toggleThumbActive
          ]} />
        </View>
      </TouchableOpacity>
    </View>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => ({
  menuItem: {
    ...globalStyles.card,
    flexDirection: 'row',
    alignItems: 'center',
    padding: aw(16),
    marginTop: aw(16),
  } as ViewStyle,
  menuIconContainer: {
    width: aw(24),
    height: aw(24),
    marginRight: aw(12),
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,
  personIcon: {
    width: aw(20),
    height: aw(20),
    position: 'relative',
  } as ViewStyle,
  personHead: {
    position: 'absolute',
    top: 0,
    left: '50%',
    transform: [{ translateX: -aw(4) }],
    width: aw(8),
    height: aw(8),
    borderRadius: aw(4),
    backgroundColor: theme.theme.colors.palette.primary600,
  } as ViewStyle,
  personBody: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: aw(10),
    borderBottomLeftRadius: aw(4),
    borderBottomRightRadius: aw(4),
    borderWidth: 1,
    borderColor: theme.theme.colors.palette.primary600,
    borderTopWidth: 0,
  } as ViewStyle,
  menuText: {
    flex: 1,
    fontSize: aw(16),
    lineHeight: aw(24),
    color: theme.theme.colors.palette.primary600,
    fontFamily: theme.theme.typography.primary.bold,
  } as TextStyle,
  toggleContainer: {
    marginLeft: 'auto',
  } as ViewStyle,
  toggleTrack: {
    width: aw(128),
    height: aw(32),
    borderRadius: aw(8),
    backgroundColor: '#E0DDE4',
    padding: aw(2),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
  } as ViewStyle,
  toggleTextContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 1,
  } as ViewStyle,
  toggleText1: {
    width: aw(46),
    fontSize: aw(14),
    lineHeight: aw(18),
    textAlign: 'center',
    color: theme.theme.colors.palette.primary500,
  } as TextStyle,
  toggleText2: {
    width: aw(72),
    fontSize: aw(14),
    lineHeight: aw(18),
    textAlign: 'center',
    color: theme.theme.colors.palette.primary500,
  } as TextStyle,
  toggleTextActive: {
    color: theme.theme.colors.palette.primary600,
  } as TextStyle,
  toggleThumb: {
    position: 'absolute',
    left: aw(2),
    top: aw(2),
    width: aw(46),
    height: aw(28),
    borderRadius: aw(8),
    backgroundColor: theme.theme.colors.palette.neutral100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    transition: 'transform 0.3s ease',
  } as ViewStyle,
  toggleThumbActive: {
    transform: [{ translateX: aw(52) }],
    width: aw(72),
  } as ViewStyle,
})
