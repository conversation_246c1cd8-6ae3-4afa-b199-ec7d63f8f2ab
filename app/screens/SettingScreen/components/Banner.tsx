
import { observer } from "mobx-react-lite"
import { View, Text, Image, TouchableOpacity, ViewStyle, TextStyle, ImageStyle, Alert } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo, useEffect } from "react"
import { aw } from "@/utils/adaptiveSize"
import { $styles as globalStyles } from "@/theme/styles"
import { useStores } from "@/models"
import * as AppleAuthentication from 'expo-apple-authentication';

// 检查设备是否支持 Apple 登录
async function checkAppleAuthAvailable() {
  return await AppleAuthentication.isAvailableAsync();
}

export const Banner = observer(function Banner() {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])
  const { authStore } = useStores()

  // 检查用户登录状态
  useEffect(() => {
    const checkAuth = async () => {
      await authStore.checkAuthStatus();
    };
    checkAuth();
  }, []);

  // 处理登录按钮点击
  const handlePress = async () => {
    try {
      const isAvailable = await checkAppleAuthAvailable();
      if (!isAvailable) {
        Alert.alert('错误', '您的设备不支持 Apple 登录');
        return;
      }

      if (authStore.isAuthenticated) {
        // 用户已登录，跳转到专业版页面或执行其他操作
        Alert.alert('已登录', `欢迎回来！`);
      } else {
        // 用户未登录，执行 Apple 登录
        const result = await authStore.signInWithApple();
        if (result.success) {
          Alert.alert('登录成功', '欢迎使用专业版！');
        } else if (result.error) {
          if (result.error !== '用户取消了登录') {
            Alert.alert('登录失败', result.error);
          }
        }
      }
    } catch (error) {
      console.error('登录出错:', error);
      Alert.alert('错误', '登录过程中发生错误');
    }
  };

  return (
    <View style={$styles.container}>
      {/* 左侧文本区域 */}
      <View style={$styles.textContainer}>
        <Text style={$styles.title}>压力从容应对</Text>
        <Text style={$styles.subtitle}>成为更好的自己</Text>
        <TouchableOpacity
          style={$styles.button}
          onPress={handlePress}
        >
          <Text style={$styles.buttonText}>
            {authStore.isAuthenticated ? '已领取永久会员' : '领取永久会员'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 右侧插画区域 */}
      <View style={$styles.illustrationContainer}>
        <Image
          source={require("assets/images/banner.png")}
          style={$styles.illustration}
          resizeMode="contain"
        />
      </View>
    </View>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    container: {
      ...globalStyles.card,
      height: aw(136),
      flexDirection: 'row',
      paddingHorizontal: aw(24),
      paddingTop: aw(4),
      paddingBottom: aw(4),
      overflow: 'hidden',
    } as ViewStyle,
    textContainer: {
      flex: 1,
      justifyContent: 'center',
      paddingRight: aw(10),
    } as ViewStyle,
    title: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
      marginBottom: aw(8),
    } as TextStyle,
    subtitle: {
      fontSize: aw(24),
      lineHeight: aw(28),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary600,
      marginBottom: aw(16),
    } as TextStyle,
    button: {
      width: aw(116),
      height: aw(32),
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#00C785',
      borderRadius: 22,
      alignSelf: 'flex-start',
    } as ViewStyle,
    buttonText: {
      width: '100%',
      color: '#fff',
      fontSize: aw(14),
      lineHeight: aw(20),
      textAlign: 'center',
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    starIcon: {
      width: aw(14),
      height: aw(14),
      marginRight: aw(4),
    } as ImageStyle,
    illustrationContainer: {
      width: aw(120),
      height: '100%',
      justifyContent: 'flex-end',
    } as ViewStyle,
    illustration: {
      width: '100%',
      height: '100%',
    } as ImageStyle,
  }
}