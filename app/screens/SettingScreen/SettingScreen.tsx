import { observer } from "mobx-react-lite"
import { Text } from "@/components/Text"
import { useStores } from "@/models"
import { requestNotificationPermission } from "@/utils/notification"
import { Button } from "@/components/Button"
import { Screen } from "@/components/Screen"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { AppStackParamList } from "@/navigators/AppNavigator"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, Linking, Platform, Alert } from "react-native"
import { aw } from "@/utils/adaptiveSize"
import { Banner } from "./components/Banner"
import { $styles as globalStyles } from "@/theme/styles"
import { RefreshModeToggle } from "./components/RefreshModeToggle"
import { MenuItem } from "./components/MenuItem"
import { Icon } from "@/components/Icon"

export const SettingScreen = observer(function SettingScreen() {
  const navigation = useNavigation<NativeStackNavigationProp<AppStackParamList>>()
  const { authenticationStore } = useStores()
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])

  // 跳转到系统通知设置
  const openNotificationSettings = async () => {
    try {
      // 先请求通知权限
      const hasPermission = await requestNotificationPermission();

      if (!hasPermission) {
        // 如果用户拒绝了权限，引导用户去设置页面手动开启
        if (Platform.OS === 'ios') {
          Alert.alert(
            '需要通知权限',
            '请在系统设置中开启通知权限，以接收重要提醒',
            [
              { text: '取消', style: 'cancel' },
              { text: '去设置', onPress: () => Linking.openSettings() }
            ]
          );
        } else {
          // Android 直接打开设置页面
          await Linking.openSettings();
        }
      } else {
        // 已经有权限，直接打开设置页面
        await Linking.openSettings();
      }
    } catch (error) {
      console.error('打开通知设置失败:', error);
      Alert.alert('错误', '无法打开通知设置，请稍后重试');
    }
  }

  function goNext() {
    console.log('导航到 Welcome 页面')
    authenticationStore.setHasSeenWelcome(false)
    navigation.navigate('Welcome')
  }

  function goToProfile() {
    console.log('导航到 ProfileScreen 页面')
    navigation.navigate('ProfileScreen')
  }

  return (
    <Screen
      preset="auto"
      contentContainerStyle={$styles.screenContentContainer}
      safeAreaEdges={["top"]}
    >
      <Text style={$styles.title}>设置</Text>
      {/* banner */}
      <Banner />
      {/* 个人基础信息 */}
      <TouchableOpacity
        style={$styles.menuItem}
        activeOpacity={0.8}
        onPress={goToProfile}
      >
        <View style={$styles.menuIconContainer}>
          <Icon icon="personalInfo" size={aw(20)} color={theme.theme.colors.palette.primary600} />
        </View>
        <Text style={$styles.menuText}>个人基础信息</Text>
        <View style={$styles.arrowIcon}>
          <Icon icon="caretRight" size={aw(18)} color={theme.theme.colors.palette.primary200} />
        </View>
      </TouchableOpacity>
      {/* HRV刷新模式 */}
      <RefreshModeToggle />
      <Text style={$styles.h2Title}>组件设置</Text>
      {/* Apple Watch表盘组件 */}
      <TouchableOpacity
        style={$styles.menuItem2}
        onPress={() => navigation.navigate('DialScreen')}
      >
        <View style={$styles.menuIconContainer}>
          <Icon icon="dialComponent" size={aw(20)} color={theme.theme.colors.palette.primary600} />
        </View>
        <Text style={$styles.menuText}>Apple Watch表盘组件</Text>
        <View style={$styles.arrowIcon}>
          <Icon icon="caretRight" size={aw(18)} color={theme.theme.colors.palette.primary200} />
        </View>
      </TouchableOpacity>
      {/* 通用设置 */}
      <Text style={$styles.h2Title}>通用设置</Text>
      <View style={$styles.card}>
        <MenuItem icon="notification" text="通知" onPress={openNotificationSettings} />
        <MenuItem icon="praise" text="好评鼓励" onPress={() => { }} />
        <MenuItem icon="share" text="分享App" onPress={() => { }} />
        <MenuItem icon="contactUs" text="联系我们" onPress={() => { }} />
        <MenuItem icon="faq" text="常见问题" onPress={() => { }} />
        <MenuItem icon="aboutUs" text="关于我们" onPress={() => { }} />
      </View>

      <Button style={{ marginTop: aw(16) }} text="Go to Welcome" onPress={() => goNext()} />
    </Screen>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,
    title: {
      marginTop: aw(30),
      fontFamily: theme.theme.typography.primary.bold,
      fontSize: aw(22),
      lineHeight: aw(26),
      color: theme.theme.colors.palette.primary600,
      marginBottom: aw(16),
    } as TextStyle,
    menuItem: {
      ...globalStyles.card,
      flexDirection: 'row',
      alignItems: 'center',
      padding: aw(16),
      marginTop: aw(16),
    } as ViewStyle,
    menuItem2: {
      ...globalStyles.card,
      flexDirection: 'row',
      alignItems: 'center',
      padding: aw(16),
    } as ViewStyle,
    menuIconContainer: {
      width: aw(24),
      height: aw(24),
      marginRight: aw(12),
      justifyContent: 'center',
      alignItems: 'center',
    } as ViewStyle,
    personIcon: {
      width: aw(20),
      height: aw(20),
      position: 'relative',
    } as ViewStyle,
    personHead: {
      position: 'absolute',
      top: 0,
      left: '50%',
      transform: [{ translateX: -aw(4) }],
      width: aw(8),
      height: aw(8),
      borderRadius: aw(4),
      backgroundColor: theme.theme.colors.palette.primary600,
    } as ViewStyle,
    personBody: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: aw(10),
      borderBottomLeftRadius: aw(4),
      borderBottomRightRadius: aw(4),
      borderWidth: 1,
      borderColor: theme.theme.colors.palette.primary600,
      borderTopWidth: 0,
    } as ViewStyle,
    menuText: {
      flex: 1,
      fontSize: aw(16),
      lineHeight: aw(24),
      color: theme.theme.colors.palette.primary600,
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    arrowIcon: {
      width: aw(24),
      height: aw(24),
      justifyContent: 'center',
      alignItems: 'flex-end',
    } as ViewStyle,
    h2Title: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
      marginTop: aw(32),
      marginBottom: aw(8),
    } as TextStyle,
    card: {
      backgroundColor: theme.theme.colors.palette.neutral100,
      borderRadius: aw(16),
      overflow: 'hidden',
    } as ViewStyle,
    divider: {
      height: 1,
      backgroundColor: theme.theme.colors.palette.neutral200,
      marginLeft: aw(52),
    } as ViewStyle,
  }
}