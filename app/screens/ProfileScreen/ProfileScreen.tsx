import { observer } from "mobx-react-lite"
import { Screen } from "@/components/Screen"
import { AppStackScreenProps } from "@/navigators/AppNavigator"
import { Header } from "@/components/Header"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo, useCallback, useEffect } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, Alert } from "react-native"
import { aw } from "@/utils/adaptiveSize"
import { Text } from "@/components/Text"
import { Icon } from "@/components/Icon"
import { $styles as globalStyles } from "@/theme/styles"
import { useHealthData } from "@/hooks/useHealthData"
import { useStores } from "@/models/helpers/useStores"

// 性别映射
const genderMap = {
  male: '男',
  female: '女',
  other: '其他',
  unknown: '未知'
}

export const ProfileScreen = observer(function ProfileScreen({ navigation }: AppStackScreenProps<"ProfileScreen">) {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])
  const { healthStore, authStore } = useStores()
  const { fetchUserInfo } = useHealthData()

  // 处理 Apple ID 点击事件
  const handleAppleIdPress = useCallback(async () => {
    navigation.navigate("LoginScreen")
    if (authStore.isAuthenticated) {
      // 显示确认对话框
      Alert.alert(
        '确认退出',
        '确定要退出当前账号吗？',
        [
          {
            text: '取消',
            style: 'cancel',
          },
          {
            text: '退出登录',
            style: 'destructive',
            onPress: () => {
              console.log('用户确认退出登录')
              authStore.signOut()
            },
          },
        ],
        { cancelable: true }
      )
    } else {
      navigation.navigate("LoginScreen")
      // try {
      //   // 未登录，尝试使用 Apple 登录
      //   const result = await authStore.signInWithApple()
      //   if (result.success) {
      //     console.log('登录成功:', result.user)
      //   } else {
      //     console.log('登录取消或失败:', result.error)
      //   }
      // } catch (error) {
      //   console.error('登录过程中出错:', error)
      // }
    }
  }, [authStore, navigation])

  // 在组件挂载时获取用户信息
  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        await fetchUserInfo()
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    }

    loadUserInfo()
  }, [fetchUserInfo])

  // 格式化用户信息
  const userInfo = healthStore.userInfo
  const formattedGender = userInfo?.gender ? (genderMap[userInfo.gender as keyof typeof genderMap] || '未知') : ''
  const formattedAge = userInfo?.age ? `${userInfo.age}岁` : ''
  const formattedHeight = userInfo?.height ? `${userInfo.height.toFixed(1)} cm` : ''
  const formattedWeight = userInfo?.weight ? `${userInfo.weight.toFixed(1)} kg` : ''

  return (
    <>
      <Header
        title="个人基础信息"
        leftIcon="caretLeft"
        onLeftPress={() => navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />
      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >
        {/* 上方卡片 - 个人基础信息 */}
        <View style={$styles.card}>
          <ProfileItem
            label="性别"
            value={formattedGender}
            styles={{
              itemContainer: $styles.itemContainer,
              itemLabel: $styles.itemLabel,
              itemRight: $styles.itemRight,
              itemValue: $styles.itemValue
            }}
          />
          <ProfileItem
            label="年龄"
            value={formattedAge}
            styles={{
              itemContainer: $styles.itemContainer,
              itemLabel: $styles.itemLabel,
              itemRight: $styles.itemRight,
              itemValue: $styles.itemValue
            }}
          />
          <ProfileItem
            label="身高"
            value={formattedHeight}
            styles={{
              itemContainer: $styles.itemContainer,
              itemLabel: $styles.itemLabel,
              itemRight: $styles.itemRight,
              itemValue: $styles.itemValue
            }}
          />
          <ProfileItem
            label="体重"
            value={formattedWeight}
            styles={{
              itemContainer: $styles.itemContainer,
              itemLabel: $styles.itemLabel,
              itemRight: $styles.itemRight,
              itemValue: $styles.itemValue
            }}
          />
        </View>
        <Text style={$styles.footerText}>数据来自苹果健康</Text>

        {/* 下方卡片 - Apple ID */}
        <View style={[$styles.card, $styles.appleIdCard]}>
          <ProfileItem
            label="我的 AppleID"
            value={authStore.isAuthenticated ? '退出登录' : '点击登录'}
            showArrow={true}
            onPress={handleAppleIdPress}
            styles={{
              itemContainer: $styles.itemContainer,
              itemLabel: $styles.itemLabel,
              itemRight: $styles.itemRight,
              itemValue: $styles.itemValue
            }}
          />
        </View>
      </Screen>
    </>

  )
})

// 个人信息项组件的属性类型
interface ProfileItemProps {
  label: string
  value: string
  showArrow?: boolean
  onPress?: () => void
  styles: {
    itemContainer: ViewStyle
    itemLabel: TextStyle
    itemRight: ViewStyle
    itemValue: TextStyle
  }
}

// 个人信息项组件
const ProfileItem = observer(function ProfileItem({
  label,
  value,
  onPress,
  styles
}: ProfileItemProps) {
  const theme = useAppTheme()

  const content = (
    <View style={styles.itemContainer}>
      <Text style={styles.itemLabel}>{label}</Text>
      <View style={styles.itemRight}>
        {value ? <Text style={styles.itemValue}>{value}</Text> : null}
        <View style={{ height: aw(20) }}>
          <Icon
            icon="caretRight"
            size={aw(16)}
            color={theme.theme.colors.palette.primary200}
          />
        </View>
      </View>
    </View >
  )

  return onPress ? (
    <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
      {content}
    </TouchableOpacity>
  ) : (
    content
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  const { colors } = theme.theme
  const itemSpacing = 16 // 项间距

  return {
    screenContentContainer: {
      paddingHorizontal: aw(16), // 屏幕边距
      gap: 16, // 卡片间距
    } as ViewStyle,
    card: {
      ...globalStyles.card,
      paddingLeft: aw(24),
      paddingVertical: aw(8),
      // 添加阴影效果
    } as ViewStyle,
    itemContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: itemSpacing,
      minHeight: 24, // 最小高度
    } as ViewStyle,
    itemLabel: {
      fontSize: 16,
      color: colors.palette.primary600,
      lineHeight: 24,
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    itemRight: {
      flexDirection: 'row',
      alignItems: 'center',
      lineHeight: aw(20),
      gap: 8, // 图标和文字间距
    } as ViewStyle,
    itemValue: {
      fontSize: aw(14),
      color: colors.palette.primary200,
      lineHeight: aw(16),
    } as TextStyle,
    footerText: {
      fontSize: aw(14),
      color: colors.palette.primary200,
      lineHeight: aw(16),
      textAlign: 'right',
      marginBottom: aw(8),
      paddingRight: aw(16),
    } as TextStyle,
    appleIdCard: {
      paddingVertical: aw(4),
      paddingLeft: aw(24),
      paddingRight: aw(16),
    } as ViewStyle,
  }
}
