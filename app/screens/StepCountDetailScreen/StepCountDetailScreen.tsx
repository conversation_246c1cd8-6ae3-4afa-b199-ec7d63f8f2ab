import { FC, useState, useEffect, useMemo } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { <PERSON>, Header, Tabs, DateSelect } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw } from "@/utils/adaptiveSize"
import { StepCountStatistics } from "./components/StepCountStatistics"
import { StepEChartsProgress } from "@/screens/StepCountDetailScreen/components/StepEChartsProgress"
import { mockData as initialMockData } from "@/data/stressData"
import { StepMetricsCard } from "./components/StepMetricsCard"
import { StepStatisticsChart } from "./components/StepStatisticsChart"

interface StepCountDetailScreenProps extends AppStackScreenProps<"StepCountDetailScreen"> { }

interface StressDataStructure {
  stepData: {
    targetStep: number
    dayStepData: {
      time: string
      value: number
      distance?: number
      calories?: number
      floors?: number
      speed?: number
      strideLength?: number
    }[]
  }
}

type TMode = "day" | "week" | "month" | "year"
type TDayStepData = StressDataStructure["stepData"]["dayStepData"]

// 获取当前时间范围
const getDateRange = (date: Date, mode: TMode) => {
  const start = new Date(date)
  const end = new Date(date)

  if (mode === "week") {
    const day = start.getDay()
    const diff = start.getDate() - day + (day === 0 ? -6 : 1) // adjust when day is sunday
    start.setDate(diff)
    end.setDate(diff + 6)
  } else if (mode === "month") {
    start.setDate(1)
    end.setMonth(end.getMonth() + 1)
    end.setDate(0)
  } else if (mode === "year") {
    start.setMonth(0, 1)
    end.setFullYear(end.getFullYear() + 1, 0, 0)
  }

  return { start, end }
}

// 获取上一个时间范围
const getPreviousDateRange = (date: Date, mode: TMode) => {
  const newDate = new Date(date)

  if (mode === "week") {
    newDate.setDate(newDate.getDate() - 7)
  } else if (mode === "month") {
    newDate.setMonth(newDate.getMonth() - 1)
  } else if (mode === "year") {
    newDate.setFullYear(newDate.getFullYear() - 1)
  }

  return getDateRange(newDate, mode)
}

// 计算步数统计
const calculateStepStatistics = (currentDate: Date, mode: TMode, dayStepData: TDayStepData) => {
  if (mode === "day") {
    return { totalSteps: 0, avgSteps: 0, comparisonSteps: 0, comparisonStepsPercent: 0 }
  }

  const currentRange = getDateRange(currentDate, mode)
  const prevRange = getPreviousDateRange(currentDate, mode)

  const currentData = dayStepData.filter((d) => {
    const recordDate = new Date(d.time)
    return recordDate >= currentRange.start && recordDate <= currentRange.end
  })

  const prevData = dayStepData.filter((d) => {
    const recordDate = new Date(d.time)
    return recordDate >= prevRange.start && recordDate <= prevRange.end
  })

  const currentTotalSteps = currentData.reduce((sum, item) => sum + item.value, 0)
  const prevTotalSteps = prevData.reduce((sum, item) => sum + item.value, 0)

  const divisor = mode === "year" ? 12 : currentData.length
  const currentAvgSteps = divisor > 0 ? Math.round(currentTotalSteps / divisor) : 0

  const comparisonSteps = currentTotalSteps - prevTotalSteps
  const comparisonStepsPercent =
    prevTotalSteps > 0 ? Math.round((comparisonSteps / prevTotalSteps) * 100) : 0

  return { totalSteps: currentTotalSteps, avgSteps: currentAvgSteps, comparisonSteps, comparisonStepsPercent }
}

// 计算步数指标
const calculateStepMetricsForMode = (currentDate: Date, mode: TMode, dayStepData: TDayStepData) => {
  let stepValue = 0
  let durationHour: number | null = null
  let durationMinute: number | null = null
  let distance: number | null = null
  let calories: number | null = null
  let floors: number | null = null
  let speed: number | null = null
  let strideLength: number | null = null
  let filteredData: TDayStepData = []

  if (mode === "day") {
    const dateStr = currentDate.toISOString().split("T")[0]
    const found = dayStepData.find((item) => item.time === dateStr)
    stepValue = found?.value ?? 0
    if (found) {
      distance = typeof found.distance === "number" ? Number((found.distance / 1000).toFixed(2)) : null
      calories = found.calories ?? null
      floors = found.floors ?? null
      speed = found.speed ?? null
      strideLength = found.strideLength ?? null
      const min = Math.round((found.value / 1000) * 10)
      durationHour = Math.floor(min / 60)
      durationMinute = min % 60
    }
  } else if (mode === "week") {
    const day = currentDate.getDay() || 7
    const monday = new Date(currentDate)
    monday.setDate(currentDate.getDate() - day + 1)
    const weekDates = Array.from({ length: 7 }, (_, i) => {
      const d = new Date(monday)
      d.setDate(monday.getDate() + i)
      return d.toISOString().slice(0, 10)
    })
    filteredData = dayStepData.filter((item) => weekDates.includes(item.time))
    stepValue = filteredData.reduce((sum, item) => sum + item.value, 0)
  } else if (mode === "month") {
    const y = currentDate.getFullYear()
    const m = (currentDate.getMonth() + 1).toString().padStart(2, "0")
    filteredData = dayStepData.filter((item) => item.time.startsWith(`${y}-${m}`))
    stepValue = filteredData.reduce((sum, item) => sum + item.value, 0)
  } else if (mode === "year") {
    const y = currentDate.getFullYear()
    filteredData = dayStepData.filter((item) => item.time.startsWith(`${y}-`))
    stepValue = filteredData.reduce((sum, item) => sum + item.value, 0)

    // 计算每月的平均指标
    const monthlyData: Record<string, {
      count: number
      totalSteps: number
      totalDistance: number
      totalCalories: number
      totalFloors: number
      totalMinutes: number
    }> = {}

    // 按月份分组计算
    filteredData.forEach(item => {
      const month = item.time.substring(0, 7) // 获取年月，如 "2023-01"
      if (!monthlyData[month]) {
        monthlyData[month] = {
          count: 0,
          totalSteps: 0,
          totalDistance: 0,
          totalCalories: 0,
          totalFloors: 0,
          totalMinutes: 0
        }
      }

      monthlyData[month].count++
      monthlyData[month].totalSteps += item.value
      monthlyData[month].totalDistance += typeof item.distance === 'number' ? item.distance : 0
      monthlyData[month].totalCalories += typeof item.calories === 'number' ? item.calories : 0
      monthlyData[month].totalFloors += typeof item.floors === 'number' ? item.floors : 0
      monthlyData[month].totalMinutes += Math.round((item.value / 1000) * 10)
    })

    // 计算平均值
    const months = Object.values(monthlyData)
    if (months.length > 0) {
      const avgMonthlySteps = Math.round(months.reduce((sum, m) => sum + m.totalSteps, 0) / months.length)
      const avgMonthlyDistance = months.reduce((sum, m) => sum + m.totalDistance, 0) / months.length
      const avgMonthlyCalories = Math.round(months.reduce((sum, m) => sum + m.totalCalories, 0) / months.length)
      const avgMonthlyFloors = Math.round(months.reduce((sum, m) => sum + m.totalFloors, 0) / months.length)
      const avgMonthlyMinutes = Math.round(months.reduce((sum, m) => sum + m.totalMinutes, 0) / months.length)

      stepValue = avgMonthlySteps
      distance = Number((avgMonthlyDistance / 1000).toFixed(2))
      calories = avgMonthlyCalories
      floors = avgMonthlyFloors
      const avgMonthlyHours = Math.floor(avgMonthlyMinutes / 60)
      const avgMonthlyMinutesRemainder = Math.round(avgMonthlyMinutes % 60)
      durationHour = avgMonthlyHours
      durationMinute = avgMonthlyMinutesRemainder

      // 年视图下不需要这些指标
      speed = null
      strideLength = null
    }
  }

  // 对于非日视图，计算聚合指标
  if (mode !== "day" && mode !== "year" && filteredData.length > 0) {
    const totalDistance = filteredData.reduce((sum, item) => {
      return sum + (typeof item.distance === 'number' ? item.distance : 0)
    }, 0)
    const totalCalories = filteredData.reduce((sum, item) => {
      return sum + (typeof item.calories === 'number' ? item.calories : 0)
    }, 0)
    const totalFloors = filteredData.reduce((sum, item) => {
      return sum + (typeof item.floors === 'number' ? item.floors : 0)
    }, 0)

    // 计算平均值
    const avgSpeed = filteredData.reduce((sum, item) => {
      return sum + (typeof item.speed === 'number' ? item.speed : 0)
    }, 0) / filteredData.length

    const avgStrideLength = filteredData.reduce((sum, item) => {
      return sum + (typeof item.strideLength === 'number' ? item.strideLength : 0)
    }, 0) / filteredData.length

    // 计算总运动时间（分钟）
    const totalMinutes = Math.round((stepValue / 1000) * 10)

    // 设置返回值
    distance = totalDistance > 0 ? Number((totalDistance / 1000).toFixed(2)) : null
    calories = totalCalories > 0 ? Math.round(totalCalories) : null
    floors = totalFloors > 0 ? Math.round(totalFloors) : null
    speed = !isNaN(avgSpeed) && isFinite(avgSpeed) ? Number(avgSpeed.toFixed(1)) : null
    strideLength = !isNaN(avgStrideLength) && isFinite(avgStrideLength)
      ? Number(avgStrideLength.toFixed(1))
      : null
    durationHour = Math.floor(totalMinutes / 60)
    durationMinute = totalMinutes % 60
  }

  return {
    stepValue,
    durationHour,
    durationMinute,
    distance,
    calories,
    floors,
    speed,
    strideLength,
  }
}

// 获取图表数据
const getChartDataForMode = (currentDate: Date, mode: TMode, dayStepData: TDayStepData) => {
  if (mode === "day" || mode === "week") {
    const day = currentDate.getDay() || 7
    const monday = new Date(currentDate)
    monday.setDate(currentDate.getDate() - day + 1)
    const weekDates = Array.from({ length: 7 }, (_, i) => {
      const d = new Date(monday)
      d.setDate(monday.getDate() + i)
      return d.toISOString().slice(0, 10)
    })
    return weekDates.map((date) => {
      const found = dayStepData.find((item) => item.time === date)
      return { date, value: found?.value ?? 0 }
    })
  }
  if (mode === "month") {
    const y = currentDate.getFullYear()
    const m = currentDate.getMonth() + 1
    const daysInMonth = new Date(y, m, 0).getDate()
    return Array.from({ length: daysInMonth }, (_, i) => {
      const day = (i + 1).toString().padStart(2, "0")
      const dateStr = `${y}-${m.toString().padStart(2, "0")}-${day}`
      const found = dayStepData.find((item) => item.time === dateStr)
      return { date: dateStr, value: found?.value ?? 0 }
    })
  }
  if (mode === "year") {
    const y = currentDate.getFullYear()
    return Array.from({ length: 12 }, (_, i) => {
      const month = (i + 1).toString().padStart(2, "0")
      const monthStr = `${y}-${month}`
      const value = dayStepData
        .filter((item) => item.time.startsWith(monthStr))
        .reduce((sum, item) => sum + item.value, 0)
      // 返回完整的日期格式，而不是简化的月份数字
      return { date: monthStr, value }
    })
  }
  return []
}

// --- Component ---

export const StepCountDetailScreen: FC<StepCountDetailScreenProps> = (_props) => {
  const theme = useAppTheme()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [activeTab, setActiveTab] = useState(0)
  const tabModes: TMode[] = ["day", "week", "month", "year"]
  const mode = tabModes[activeTab]
  const [targetStep, setTargetStep] = useState(0)
  const [dayStepData, setDayStepData] = useState<TDayStepData>([])

  // 组件挂载时设置初始值
  useEffect(() => {
    setTargetStep(initialMockData.stepData.targetStep)
    setDayStepData([...initialMockData.stepData.dayStepData])
  }, [])

  const stepStatistics = useMemo(
    () => calculateStepStatistics(currentDate, mode, dayStepData),
    [currentDate, mode, dayStepData],
  )

  // 计算步数指标
  const {
    stepValue,
    durationHour,
    durationMinute,
    distance,
    calories,
    floors,
    speed,
    strideLength,
  } = useMemo(
    () => calculateStepMetricsForMode(currentDate, mode, dayStepData),
    [currentDate, mode, dayStepData],
  )

  // 获取图表数据
  const chartData = useMemo(
    () => {
      const data = getChartDataForMode(currentDate, mode, dayStepData)
      return data
    },
    [currentDate, mode, dayStepData],
  )

  const $styles = useMemo(() => createStyles(theme), [theme])

  return (
    <>
      <Header
        title="步数"
        leftIcon="caretLeft"
        onLeftPress={() => _props.navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />

      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >
        <View style={$styles.container}>
          {/* 选项卡 */}
          <Tabs
            activeTab={activeTab}
            onTabChange={(index) => {
              setActiveTab(index)
              setCurrentDate(new Date())
            }}
          />
          {/* 日期选择 */}
          <DateSelect
            style={{ marginTop: aw(16) }}
            mode={mode}
            value={currentDate}
            onChange={setCurrentDate}
          />
          {/* 步数统计 */}
          {mode === "day" ? (
            <StepEChartsProgress value={stepValue} target={targetStep} />
          ) : (
            <StepCountStatistics {...stepStatistics} mode={mode} />
          )}
          {/* 步数相关指标 */}
          <StepMetricsCard
            durationHour={durationHour}
            durationMinute={durationMinute}
            distance={distance}
            calories={calories}
            floors={floors}
            speed={speed}
            strideLength={strideLength}
            mode={mode}
          />
          {/* 步数统计图表 */}
          <StepStatisticsChart data={chartData} target={targetStep} mode={mode} />
        </View>
      </Screen>
    </>
  )
}

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,
  }
}