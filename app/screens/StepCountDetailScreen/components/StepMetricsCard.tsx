import { FC, useMemo } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { $styles as globalStyles } from "@/theme"
import { Text } from "@/components/Text"
import { aw } from "@/utils/adaptiveSize"
import { useAppTheme } from "@/utils/useAppTheme"

type Mode = 'day' | 'week' | 'month' | 'year'

interface StepMetricsCardProps {
  durationHour: number | null
  durationMinute: number | null
  distance: number | null // km
  calories: number | null // kcal
  floors: number | null // 层
  speed: number | null // km/h
  strideLength: number | null // cm
  mode: Mode
}

export const StepMetricsCard: FC<StepMetricsCardProps> = ({
  durationHour,
  durationMinute,
  distance,
  calories,
  floors,
  speed,
  strideLength,
  mode = 'day',
}) => {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])
  // 年视图下只显示4个指标
  if (mode === 'year') {
    return (
      <View style={$styles.card}>
        {/* 左列 */}
        <View style={$styles.col}>
          {/* 每月步行时长 */}
          <View style={$styles.row}>
            <View>
              <Text style={$styles.label}>每月步行时长</Text>
              <View style={$styles.valueRow}>
                <Text style={$styles.value}>{durationHour !== null ? durationHour : '--'}</Text>
                <Text style={$styles.unit}>小时</Text>
                <Text style={$styles.valueMid}>{durationMinute !== null ? durationMinute : '--'}</Text>
                <Text style={$styles.unit}>分钟</Text>
              </View>
            </View>
          </View>
          {/* 每月平均步行卡路里 */}
          <View style={$styles.row2}>
            <View>
              <Text style={$styles.label}>每月平均步行卡路里</Text>
              <View style={$styles.valueRow}>
                <Text style={$styles.value}>{calories !== null ? calories.toLocaleString() : '--'}</Text>
                <Text style={$styles.unit}>kcal</Text>
              </View>
            </View>
          </View>
        </View>
        {/* 右列 */}
        <View style={[$styles.col, { marginLeft: aw(24) }]}>
          {/* 每月平均步行距离 */}
          <View style={$styles.row}>
            <View>
              <Text style={$styles.label}>每月平均步行距离</Text>
              <View style={$styles.valueRow}>
                <Text style={$styles.value}>{distance !== null ? distance.toLocaleString() : '--'}</Text>
                <Text style={$styles.unit}>km</Text>
              </View>
            </View>
          </View>
          {/* 每月平均爬楼层 */}
          <View style={$styles.row2}>
            <View>
              <Text style={$styles.label}>每月平均爬楼层</Text>
              <View style={$styles.valueRow}>
                <Text style={$styles.value}>{floors !== null ? floors.toLocaleString() : '--'}</Text>
                <Text style={$styles.unit}>层</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    )
  }

  // 非年视图显示所有指标
  return (
    <View style={$styles.card}>
      {/* 左列 */}
      <View style={$styles.col}>
        {/* 步行时长 */}
        <View style={$styles.row}>
          <View>
            <Text style={$styles.label}>步行时长</Text>
            <View style={$styles.valueRow}>
              <Text style={$styles.value}>{durationHour !== null ? durationHour : '--'}</Text>
              <Text style={$styles.unit}>小时</Text>
              <Text style={$styles.valueMid}>{durationMinute !== null ? durationMinute : '--'}</Text>
              <Text style={$styles.unit}>分钟</Text>
            </View>
          </View>
        </View>
        {/* 总卡路里 */}
        <View style={$styles.row}>
          <View>
            <Text style={$styles.label}>总卡路里</Text>
            <View style={$styles.valueRow}>
              <Text style={$styles.value}>{calories !== null ? calories.toLocaleString() : '--'}</Text>
              <Text style={$styles.unit}>kcal</Text>
            </View>
          </View>
        </View>
        {/* 步行速度 */}
        <View style={$styles.row2}>
          <View>
            <Text style={$styles.label}>步行速度</Text>
            <View style={$styles.valueRow}>
              <Text style={$styles.value}>{speed !== null ? speed : '--'}</Text>
              <Text style={$styles.unit}>km/h</Text>
            </View>
          </View>
        </View>
      </View>
      {/* 右列 */}
      <View style={[$styles.col, { marginLeft: aw(24) }]}>
        {/* 总距离 */}
        <View style={$styles.row}>
          <View>
            <Text style={$styles.label}>总距离</Text>
            <View style={$styles.valueRow}>
              <Text style={$styles.value}>{distance !== null ? distance.toLocaleString() : '--'}</Text>
              <Text style={$styles.unit}>km</Text>
            </View>
          </View>
        </View>
        {/* 已爬楼层 */}
        <View style={$styles.row}>
          <View>
            <Text style={$styles.label}>已爬楼层</Text>
            <View style={$styles.valueRow}>
              <Text style={$styles.value}>{floors !== null ? floors.toLocaleString() : '--'}</Text>
              <Text style={$styles.unit}>层</Text>
            </View>
          </View>
        </View>
        {/* 步行步长 */}
        <View style={$styles.row2}>
          <View>
            <Text style={$styles.label}>步行步长</Text>
            <View style={$styles.valueRow}>
              <Text style={$styles.value}>{strideLength !== null ? strideLength : '--'}</Text>
              <Text style={$styles.unit}>cm</Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    card: {
      ...globalStyles.card,
      flexDirection: "row",
      paddingVertical: aw(20),
      paddingHorizontal: aw(20),
      marginBottom: aw(16),
    } as ViewStyle,
    col: {
      flex: 1,
      justifyContent: "space-between",
    } as ViewStyle,
    row: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: aw(24),
    } as ViewStyle,
    row2: {
      flexDirection: "row",
      alignItems: "center",
    } as ViewStyle,
    label: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
      marginBottom: aw(8),
    } as TextStyle,
    valueRow: {
      flexDirection: "row",
      alignItems: "center",
    } as ViewStyle,
    value: {
      fontSize: aw(24),
      lineHeight: aw(26),
      color: theme.theme.colors.palette.primary600,
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    valueMid: {
      fontSize: aw(24),
      lineHeight: aw(26),
      color: theme.theme.colors.palette.primary600,
      fontFamily: theme.theme.typography.primary.bold,
      marginLeft: aw(4),
    } as TextStyle,
    unit: {
      fontSize: aw(13),
      color: theme.theme.colors.palette.primary100,
      marginLeft: aw(4),
    } as TextStyle,
  }
}
