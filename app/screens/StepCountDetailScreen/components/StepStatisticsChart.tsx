import { FC, useRef, useMemo, useEffect } from "react"
import { View, ViewStyle, Dimensions, TextStyle } from "react-native"
import { Path, Svg } from "react-native-svg"
import echarts from "@/utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, af } from "@/utils/adaptiveSize"
import { Text } from "@/components"
import { $styles as globalStyles } from "@/theme/styles"
import { extractMonth } from "app/utils/formatDate"

// 自定义虚线组件
const DashedLine = ({ width = 24, height = 1, color = '#000', dashWidth = 4, dashGap = 2 }) => {
  const path = Array.from({ length: Math.ceil(width / (dashWidth + dashGap)) })
    .map((_, i) => `M${i * (dashWidth + dashGap)} 0h${dashWidth}`)
    .join(' ')

  return (
    <Svg width={width} height={height}>
      <Path
        d={path}
        stroke={color}
        strokeWidth={height}
        strokeLinecap="round"
      />
    </Svg>
  )
}

// Interfaces
interface StepStatisticsData {
  date: string
  value: number
}

interface StepStatisticsChartProps {
  title?: string
  data: StepStatisticsData[]
  target: number
  mode: "day" | "week" | "month" | "year"
}

// 目标达成度指标
const TargetCompletionMetrics: FC<Omit<StepStatisticsChartProps, "title">> = ({ data, target, mode }) => {
  const theme = useAppTheme()
  const $styles = useMemo(() => createMetricStyles(theme), [theme])

  const metrics = useMemo(() => {
    const completedDays = data.filter((item) => item.value >= target).length
    const totalDays = data.length
    const completionRate = totalDays > 0 ? Math.round((completedDays / totalDays) * 100) : 0

    const validData = data.filter((item) => item.value > 0)

    // 计算最高和最低的日期
    const highestDay = validData.reduce((max, item) => (item.value > max.value ? item : max), {
      value: 0,
      date: "",
    })

    const lowestDay = validData.reduce(
      (min, item) => (item.value < min.value ? item : min),
      {
        value: Infinity,
        date: "",
      },
    )

    // 直接使用传入的月份数据
    const monthlyAverages = data.map(item => ({
      month: item.date,
      average: item.value
    }))

    const highestMonth = monthlyAverages.reduce((max, item) =>
      item.average > max.average ? item : max,
      { month: "", average: 0 }
    )
    const lowestMonth = monthlyAverages.reduce((min, item) =>
      item.average < min.average && item.average > 0 ? item : min,
      { month: "", average: Infinity }
    )

    return {
      completedDays,
      completionRate,
      highestDay,
      lowestDay,
      highestMonth,
      lowestMonth
    }
  }, [data, target])

  if (mode === "day" || data.length === 0) return null

  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    const date = new Date(dateString)
    return `${(date.getMonth() + 1).toString().padStart(2, "0")}/${date
      .getDate()
      .toString()
      .padStart(2, "0")}`
  }

  // 提取月份数字部分
  const formatMonth = (dateString: string): string => {
    const month = extractMonth(dateString)
    return month ? `${month}` : '--'
  }

  if (mode === "year") {
    return (
      <View style={$styles.container}>
        <View style={$styles.row}>
          <View style={$styles.card}>
            <Text style={$styles.label}>最高月均步数</Text>
            <View style={$styles.valueContainer}>
              <Text style={$styles.value}>
                {formatMonth(metrics.highestMonth.month)}
              </Text>
              <Text style={$styles.unit}>月</Text>
            </View>
            <Text style={$styles.date}>
              {metrics.highestMonth.average.toLocaleString() + '步'}
            </Text>
          </View>
          <View style={$styles.card}>
            <Text style={$styles.label}>最低月均步数</Text>
            <View style={$styles.valueContainer}>
              <Text style={$styles.value}>
                {formatMonth(metrics.lowestMonth.month)}
              </Text>
              <Text style={$styles.unit}>月</Text>
            </View>
            <Text style={$styles.date}>
              {metrics.lowestMonth.average !== Infinity ? metrics.lowestMonth.average.toLocaleString() + '步' : '--'}
            </Text>
          </View>
        </View>
      </View>
    )
  }

  return (
    <View style={$styles.container}>
      <View style={$styles.row}>
        <View style={$styles.card}>
          <Text style={$styles.label}>完成</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.value}>{metrics.completedDays}</Text>
            <Text style={$styles.unit}>天</Text>
          </View>
        </View>
        <View style={$styles.card}>
          <Text style={$styles.label}>完成率</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.value}>{metrics.completionRate}</Text>
            <Text style={$styles.unit}>%</Text>
          </View>
        </View>
      </View>
      <View style={$styles.row}>
        <View style={$styles.card}>
          <Text style={$styles.label}>最高一天</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.value}>{metrics.highestDay.value}</Text>
            <Text style={$styles.unit}>步</Text>
          </View>
          <Text style={$styles.date}>{formatDate(metrics.highestDay.date)}</Text>
        </View>
        <View style={$styles.card}>
          <Text style={$styles.label}>最低一天</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.value}>
              {isFinite(metrics.lowestDay.value) ? metrics.lowestDay.value : 0}
            </Text>
            <Text style={$styles.unit}>步</Text>
          </View>
          <Text style={$styles.date}>{formatDate(metrics.lowestDay.date)}</Text>
        </View>
      </View>
    </View>
  )
}

const { width: screenWidth } = Dimensions.get("window")
const CHART_HEIGHT = aw(225)
const CHART_WIDTH = screenWidth - aw(48)

export const StepStatisticsChart: FC<StepStatisticsChartProps> = ({ data, target, mode }) => {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)

  const { dates, values, yAxisMax } = useMemo(() => {
    const dates = data.map((item) => {
      if (!/^\d{4}-\d{2}-\d{2}$/.test(item.date)) return item.date
      const d = new Date(item.date)
      if (mode === 'year') {
        // 年视图显示月份
        return `${d.getMonth() + 1}`
      } else if (mode === 'month') {
        // 月视图显示日期
        return `${d.getDate()}`
      } else {
        // 其他情况保持原来的格式
        return `${d.getMonth() + 1}/${d.getDate()}`
      }
    })
    const values = data.map((item) => item.value)
    const maxValue = Math.max(target, ...values)
    let yAxisMax = maxValue
    if (maxValue > 2000) {
      yAxisMax = Math.ceil(maxValue / 1000) * 1000
    } else if (maxValue > 1000) {
      yAxisMax = Math.ceil(maxValue / 500) * 500
    } else if (maxValue > 500) {
      yAxisMax = Math.ceil(maxValue / 250) * 250
    } else if (maxValue > 200) {
      yAxisMax = Math.ceil(maxValue / 100) * 100
    } else {
      yAxisMax = Math.ceil(maxValue / 50) * 50
    }
    if (yAxisMax < target) {
      yAxisMax = target
    }
    return { dates, values, yAxisMax }
  }, [data, target])

  const chartOption = useMemo(() => {
    const adjustedTarget = mode === 'year' ? target * 30 : target
    const adjustedYAxisMax = Math.max(adjustedTarget, yAxisMax)
    return {
      grid: {
        left: aw(10),  // 增加左边距
        right: aw(0), // 增加右边距
        top: aw(10),
        bottom: aw(10),
        height: CHART_HEIGHT - aw(10),
        width: CHART_WIDTH,
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: dates,
        boundaryGap: true, // 让柱状图从y轴开始
        axisLine: {
          show: true,
          lineStyle: {
            color: theme.theme.colors.palette.primary100,
            width: 1,
            type: "dashed",
          },
        },
        axisTick: { show: false },
        axisLabel: {
          color: theme.theme.colors.palette.primary100,
          fontSize: aw(12),
          formatter: (value: string) => {
            if (mode === 'year') {
              // 在年视图下，只返回月份数字
              const month = extractMonth(value)
              return month ? `${month}` : value
            }
            return value
          },
        },
      },
      yAxis: {
        type: "value",
        max: adjustedYAxisMax,
        min: 0,
        position: "right",
        axisLine: { show: false },
        axisLabel: {
          show: true,
          customValues: [adjustedTarget],
          color: theme.theme.colors.palette.primary200,
          formatter: (value: number) => {
            if (value === adjustedTarget) {
              return `${Math.floor(adjustedTarget / 1000)}k`
            }
            return value
          },
        },
        axisTick: {
          alignWithLabel: true,
          customValues: [adjustedYAxisMax / 4, adjustedYAxisMax / 4 * 2, adjustedYAxisMax / 4 * 3, adjustedYAxisMax, adjustedTarget],
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: theme.theme.colors.palette.primary100,
            type: "dashed",
            width: 1,
          },
        },
      },
      series: [
        {
          type: "bar",
          data: values,
          barWidth: mode === 'month' ? aw(8) : aw(16), // 月视图时柱子更窄
          itemStyle: {
            color: "#FF7A2F",
            borderRadius: [aw(8), aw(8), 0, 0],
          },
          barGap: 0, // 确保没有额外的柱子间距
        },
      ],
    }
  }, [dates, values, yAxisMax, theme])

  useEffect(() => {
    if (chartRef.current) {
      let chartInstance: any
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          renderer: "svg",
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }
      return () => {
        chartInstance?.dispose()
      }
    }
    return undefined
  }, [chartOption])

  const $styles = useMemo(() => createStyles(theme, mode), [theme, mode])

  return (
    <View style={$styles.container}>
      <Text style={$styles.titleText}>步数统计</Text>
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>
      {/* 图例 */}
      <View style={$styles.legendRow}>
        <View style={$styles.legendItem}>
          <View style={$styles.legendDashContainer}>
            <DashedLine
              width={aw(24)}
              height={1}
              color={theme.theme.colors.palette.primary200}
              dashWidth={4}
              dashGap={2}
            />
          </View>
          <Text style={$styles.legendText}>目标线</Text>
        </View>
        <View style={$styles.legendItem}>
          <View style={$styles.legendDot} />
          <Text style={$styles.legendText}>步数</Text>
        </View>
      </View>
      {/* 目标完成指标卡片 */}
      <TargetCompletionMetrics data={data} target={target} mode={mode} />
    </View>
  )
}

// --- Styles ---
const createStyles = (theme: ReturnType<typeof useAppTheme>, mode: string) => {
  return {
    container: {
      ...globalStyles.card,
    } as ViewStyle,
    titleText: {
      fontSize: af(16),
      fontFamily: theme.theme.typography.primary.bold,
      lineHeight: af(18),
      color: theme.theme.colors.text,
      marginBottom: aw(16),
    } as TextStyle,
    chartContainer: {
      position: "relative",
      alignItems: "center",
      justifyContent: "center",
      height: CHART_HEIGHT,
      width: "100%",
    } as ViewStyle,
    targetLabel: {
      position: "absolute",
      right: mode === 'month' ? aw(0) : aw(0),
      color: theme.theme.colors.palette.primary200,
      fontSize: aw(13),
    } as TextStyle,
    legendRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      marginTop: aw(16),
      gap: aw(16),
    } as ViewStyle,
    legendItem: {
      flexDirection: "row",
      alignItems: "center",
    } as ViewStyle,
    legendDashContainer: {
      justifyContent: 'center',
      marginRight: aw(6),
    } as ViewStyle,
    legendDot: {
      width: aw(10),
      height: aw(10),
      borderRadius: aw(5),
      backgroundColor: "#FF7A2F",
      marginRight: aw(6),
    } as ViewStyle,
    legendText: {
      color: theme.theme.colors.palette.primary200,
      fontSize: aw(14),
    } as TextStyle,
  }
}

const createMetricStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    container: {
      marginTop: aw(16),
    } as ViewStyle,
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: aw(10),
    } as ViewStyle,
    card: {
      backgroundColor: theme.theme.colors.palette.neutral200,
      borderRadius: aw(12),
      padding: aw(12),
      width: "48%",
    } as ViewStyle,
    fullWidthCard: {
      backgroundColor: theme.theme.colors.palette.neutral200,
      borderRadius: aw(12),
      padding: aw(12),
      width: "100%",
    } as ViewStyle,
    label: {
      fontSize: aw(14),
      lineHeight: aw(16),
      marginBottom: aw(16),
      color: theme.theme.colors.palette.primary200,
    } as TextStyle,
    valueContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: aw(4),
    } as ViewStyle,
    value: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
    } as TextStyle,
    unit: {
      fontSize: aw(12),
      color: theme.theme.colors.textDim,
      marginLeft: aw(4),
    } as TextStyle,
    date: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
      marginTop: aw(8),
    } as TextStyle,
  }
}