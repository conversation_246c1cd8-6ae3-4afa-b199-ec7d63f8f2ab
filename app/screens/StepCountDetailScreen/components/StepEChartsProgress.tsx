import { FC, useRef, useEffect, useMemo } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import echarts from "../../../utils/echarts"
import { Icon } from "../../../components/Icon"
import { Text } from "../../../components/Text"
import { aw } from "@/utils/adaptiveSize"
import { useAppTheme } from "@/utils/useAppTheme"


interface StepEChartsProgressProps {
  value: number
  target: number
  size?: number // 直径，默认208
}

const CHART_SIZE = aw(208)
const PROGRESS_WIDTH = 19

export const StepEChartsProgress: FC<StepEChartsProgressProps> = ({ value, target, size = CHART_SIZE }) => {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)
  const percent = Math.max(0, Math.min(1, value / target))
  const percentText = `${Math.round(percent * 100)}%`

  const chartOption = useMemo(() => ({
    series: [
      {
        type: "gauge",
        radius: "100%",
        center: ["50%", "50%"],
        startAngle: 240,
        endAngle: -60,
        min: 0,
        max: 100,
        progress: {
          show: true,
          width: PROGRESS_WIDTH,
          roundCap: true,
          itemStyle: {
            color: "#FF7A2F",
          },
        },
        axisLine: {
          show: true,
          roundCap: true,
          lineStyle: {
            width: PROGRESS_WIDTH,
            color: [
              [percent < 0.75 ? percent : 0.75, '#FF7A2F'],
              [0.999999, '#fff'],
              [1, 'rgba(0,0,0,0)']
            ],
          },
        },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        pointer: { show: false },
        detail: { show: false },
        data: [{ value: percent * 100 }],
        z: 2,
      },
    ],
  }), [percent])

  useEffect(() => {
    if (chartRef.current) {
      const chartInstance = echarts.init(chartRef.current, "light", {
        renderer: "svg",
        width: size,
        height: size,
      })
      chartInstance.setOption(chartOption)
      return () => chartInstance.dispose()
    }
    return undefined
  }, [chartOption, size])

  const $styles = useMemo(() => createStyles(theme, size), [theme, size])

  return (
    <View style={$styles.container}>
      <View style={$styles.chart}>
        <SvgChart ref={chartRef} />
      </View>
      {/* 脚印icon，无虚线，橙色 */}
      <View style={$styles.iconBox}>
        <Icon icon="stepIcon" color="#FF7A2F" size={aw(32)} />
      </View>
      {/* 步数和目标 */}
      <View style={$styles.centerBox}>
        <Text style={$styles.valueText}>{value}</Text>
        <Text style={$styles.targetText}>/{target}</Text>
      </View>
      {/* 百分比 */}
      <View style={$styles.percentBox}>
        <Text style={$styles.percentText}>{percentText}</Text>
      </View>
    </View>
  )
}

const createStyles = (theme: ReturnType<typeof useAppTheme>, size: number) => {
  return {
    container: {
      height: aw(208),
      alignItems: "center",
      justifyContent: "center",
      position: "relative",
      marginTop: aw(24),
      marginBottom: aw(16),
    } as ViewStyle,

    chart: {
      width: size,
      height: size,
    } as ViewStyle,
    iconBox: {
      position: "absolute",
      top: aw(40),
      left: 0,
      right: 0,
      alignItems: "center",
      zIndex: 3,
    } as ViewStyle,

    centerBox: {
      position: "absolute",
      top: aw(85),
      left: 0,
      right: 0,
      alignItems: "center",
      zIndex: 4,
    } as ViewStyle,
    valueText: {
      fontSize: aw(36),
      color: theme.theme.colors.palette.primary600,
      fontFamily: theme.theme.typography.primary.bold,
      textAlign: "center",
      lineHeight: aw(40),
    } as TextStyle,
    targetText: {
      fontSize: aw(16),
      color: theme.theme.colors.palette.primary200,
      textAlign: "center",
      marginTop: aw(8),
      fontFamily: theme.theme.typography.primary.bold,
      lineHeight: aw(18),
    } as TextStyle,
    percentBox: {
      position: "absolute",
      left: 0,
      right: 0,
      bottom: 0,
      alignItems: "center",
      zIndex: 4,
    } as ViewStyle,
    percentText: {
      fontSize: aw(24),
      color: "#FF7A2F",
      fontFamily: theme.theme.typography.primary.bold,
      textAlign: "center",
      lineHeight: aw(32),
    } as TextStyle,
  }
}