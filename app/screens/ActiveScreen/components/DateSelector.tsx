import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { format, addDays, isSameDay, isBefore } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { useAppTheme } from '@/utils/useAppTheme';
import { useMemo } from 'react';
import { ViewStyle, TextStyle } from 'react-native';
import { aw } from '@/utils/adaptiveSize';

interface DateSelectorProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const DateSelector: React.FC<DateSelectorProps> = ({ selectedDate, onDateSelect }) => {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])
  // 获取本周的日期范围
  const getWeekDates = () => {
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay()); // 设置为本周日

    return Array.from({ length: 7 }).map((_, index) => {
      const date = addDays(startOfWeek, index);
      return date;
    });
  };

  const weekDates = getWeekDates();

  // 获取星期几的缩写（英文）
  const getDayAbbreviation = (date: Date) => {
    return format(date, 'EEE', { locale: enUS });
  };

  // 获取日期数字
  const getDayNumber = (date: Date) => {
    return format(date, 'd');
  };

  return (
    <View style={$styles.container}>
      <View style={$styles.daysContainer}>
        {weekDates.map((date) => {
          const isSelected = isSameDay(date, selectedDate);
          return (
            <TouchableOpacity
              key={date.toString()}
              style={$styles.dayItem}
              onPress={() => onDateSelect(date)}
              activeOpacity={0.7}
            >
              <View style={[$styles.dayContent, isSelected && $styles.selectedDay]}>
                <Text style={[
                  $styles.dateWeekText,
                  isSelected && $styles.selectedDayText
                ]}>
                  {getDayAbbreviation(date)}
                </Text>
                <Text style={[
                  $styles.dateText,
                  (isBefore(date, new Date()) || isSameDay(date, new Date())) && $styles.pastDateText,
                  isSelected && $styles.selectedDateText,
                ]}>
                  {getDayNumber(date)}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    container: {
      marginTop: aw(10),
      marginBottom: aw(16),
    } as ViewStyle,
    daysContainer: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    } as ViewStyle,
    dayItem: {
      alignItems: 'center',
      borderRadius: 16,
    } as ViewStyle,
    dayContent: {
      width: aw(50),
      alignItems: 'center',
      paddingVertical: 8,
      borderRadius: 12,
    } as ViewStyle,
    selectedDay: {
      backgroundColor: '#E0DDE4',
    } as ViewStyle,
    dayText: {
      fontSize: 14,
      color: '#666',
      marginBottom: 4,
    } as TextStyle,
    selectedDayText: {
      width: aw(36),
      height: 16,
      lineHeight: 16,
      color: '#fff',
      fontFamily: theme.theme.typography.primary.bold,
      textAlign: 'center',
      backgroundColor: theme.theme.colors.palette.primary301,
      borderRadius: 8,
      overflow: 'hidden',
    } as TextStyle,
    dateWeekText: {
      fontSize: 12,
      color: '#D9D6DD',
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    dateText: {
      fontSize: 16,
      color: '#D9D6DD',
      fontFamily: theme.theme.typography.primary.bold,
    } as TextStyle,
    selectedDateText: {
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    pastDateText: {
      color: '#8489B4',
    } as TextStyle,
  }
}

export default DateSelector;
