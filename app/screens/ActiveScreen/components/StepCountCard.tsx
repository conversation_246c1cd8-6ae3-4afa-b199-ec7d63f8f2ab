import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, Dimensions } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo, useRef, useEffect } from "react"
import { $styles as globalStyles } from "@/theme/styles"
import { aw, ah } from "@/utils/adaptiveSize"
import { Icon } from "@/components/Icon"
import { Text } from "@/components/Text"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import echarts from "@/utils/echarts"

// Interfaces
interface StepStatisticsData {
  date: string
  value: number
}

interface StepStatisticsChartProps {
  title?: string
  data: StepStatisticsData[]
  target: number
}

const { width: screenWidth } = Dimensions.get("window")
const CHART_HEIGHT = aw(50)
const CHART_WIDTH = screenWidth / 2 - aw(56)

export const StepCountCard = observer(function StepCountCard({ data, target }: StepStatisticsChartProps) {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)
  const $styles = useMemo(() => createStyles(theme), [theme])



  const { values, yAxisMax } = useMemo(() => {
    // 确保有24个数据点，不足的补0
    const paddedData = [...data]

    const values = paddedData.slice(0, 24).map((item) => item.value)
    const maxValue = Math.max(target, ...values)
    let yAxisMax = maxValue
    if (maxValue > 2000) {
      yAxisMax = Math.ceil(maxValue / 1000) * 1000
    } else if (maxValue > 1000) {
      yAxisMax = Math.ceil(maxValue / 500) * 500
    } else if (maxValue > 500) {
      yAxisMax = Math.ceil(maxValue / 250) * 250
    } else if (maxValue > 200) {
      yAxisMax = Math.ceil(maxValue / 100) * 100
    } else {
      yAxisMax = Math.ceil(maxValue / 50) * 50
    }
    if (yAxisMax < target) {
      yAxisMax = target
    }
    return { values, yAxisMax }
  }, [data, target])


  const chartOption = useMemo(() => {
    // 创建24小时的数据点，如果没有数据则显示为0
    const hoursData = Array(24).fill(0);
    data.forEach((item, index) => {
      hoursData[index] = item.value;
    });

    return {
      grid: {
        left: aw(0),
        right: aw(0),
        top: aw(0),
        bottom: aw(0),
        width: CHART_WIDTH,
        height: CHART_HEIGHT,
      },
      xAxis: {
        type: 'category',
        data: Array.from({ length: 24 }, (_, i) => i + 1),
        show: false,
      },
      yAxis: {
        type: 'value',
        show: false,
        min: 0,
        max: yAxisMax,
      },
      series: [
        // 柱状图系列（有数据的柱子）
        {
          type: 'bar',
          barWidth: aw(4),
          data: hoursData.map((value) => ({
            value,
            itemStyle: {
              color: value > 0 ? '#FF7A2F' : 'transparent',
            },
          })),
          itemStyle: {
            borderRadius: [aw(4), aw(4), 0, 0],
          },
          z: 2,
        },
        // 散点图系列（无数据时的圆点）
        {
          type: 'scatter',
          symbolSize: 4,
          symbol: 'circle',
          data: hoursData.map((value, index) => ({
            value: [index, 1],
            itemStyle: {
              color: value > 0 ? 'transparent' : '#FFE6DD',
              opacity: 1,
            },
          })),
          z: 1,
          yAxisIndex: 0,
          // 确保散点图不被裁剪
          clip: false,
          // 设置散点图的坐标系
          coordinateSystem: 'cartesian2d',
        },
      ]
    };
  }, [data, aw, CHART_WIDTH, CHART_HEIGHT, yAxisMax]);

  useEffect(() => {
    if (chartRef.current) {
      let chartInstance: any
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          renderer: "svg",
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }
      return () => {
        chartInstance?.dispose()
      }
    }
    return undefined
  }, [chartOption])

  return (
    <View style={$styles.container}>
      <Icon style={$styles.icon} icon="stepIcon" size={aw(32)} useOriginalColor />
      <View style={$styles.contentContainer}>
        <View style={$styles.stepCountContainer}>
          <Text style={$styles.currentStep}>2004</Text>
          <Text style={$styles.targetContainer}>/{target}步</Text>
        </View>
        <Text style={$styles.label}>步数</Text>
      </View>
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>
    </View>
  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    container: {
      ...globalStyles.card,
    } as ViewStyle,
    contentContainer: {
      justifyContent: 'center',
      marginBottom: aw(8),
    } as ViewStyle,
    icon: {
      marginBottom: aw(8),
    },
    stepCountContainer: {
      flexDirection: 'row',
      alignItems: 'baseline',
      marginBottom: ah(4),
    } as ViewStyle,
    currentStep: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    targetContainer: {
      marginLeft: aw(4),
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary600,
    } as ViewStyle,
    label: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.textDim,
    } as TextStyle,
    chartContainer: {
      position: "relative",
      alignItems: "center",
      justifyContent: "center",
      height: CHART_HEIGHT,
      width: "100%",
      // paddingHorizontal: aw(16),
    } as ViewStyle,
  }
}