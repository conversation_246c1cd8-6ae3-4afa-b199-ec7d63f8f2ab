import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, ImageStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils"
import { useMemo, useEffect, useRef } from "react"
import { aw, ah } from "@/utils"
import { $styles as globalStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"


interface FitnessData {
  activityCalories: number,
  targetActivityCalories: number,
  exerciseMinutes: number,
  targetExerciseMinutes: number,
  standHours: number,
  targetStandHours: number,
}


const CHART_HEIGHT = aw(48)
const CHART_WIDTH = aw(48)
/**
 * 健身卡片组件
 */
export const FitnessCard = observer(function FitnessCard({ fitnessData }: { fitnessData: FitnessData }) {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)
  const $styles = useMemo(() => createStyles(theme), [theme])


  const chartOption = useMemo(() => {
    const activityPercentage = Math.min((fitnessData.activityCalories / fitnessData.targetActivityCalories) * 100, 100)
    const exercisePercentage = Math.min((fitnessData.exerciseMinutes / fitnessData.targetExerciseMinutes) * 100, 100)
    const standPercentage = Math.min((fitnessData.standHours / fitnessData.targetStandHours) * 100, 100)

    return {
      series: [
        {
          type: "gauge",
          radius: "100%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(5),
            roundCap: true,
            itemStyle: {
              color: "#EA5E70", // Red for activity
              borderColor: '#1B225C',
              borderWidth: 2,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(1),
              color: [[1, '#1B225C']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: activityPercentage }],
        },
        {
          type: "gauge",
          radius: "77%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(5),
            roundCap: true,
            itemStyle: {
              color: "#5CCBDA", // Teal for exercise
              borderColor: '#1B225C',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(1),
              color: [[1, '#1B225C']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: exercisePercentage }],
        },
        {
          type: "gauge",
          radius: "50%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(5),
            roundCap: true,
            itemStyle: {
              color: "#699AE5", // Blue for stand
              borderColor: '#1B225C',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(4),
              color: [[1, '#1B225C']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: standPercentage }],
        },
      ],
    }
  }, [fitnessData])

  useEffect(() => {
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      let chartInstance: ECharts | undefined
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          renderer: "svg",
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }

      return () => {
        if (chartInstance) {
          chartInstance.dispose()
        }
      }
    }
    return undefined
  }, [chartOption])


  return (
    <View style={$styles.container}>
      <View style={$styles.header}>
        <View style={$styles.titleContainer}>
          <View style={$styles.iconContainer}>
            <Icon icon="fitness" size={aw(16)} useOriginalColor />
          </View>
          <Text style={$styles.title} text="健身" />
        </View>
      </View>


      {/* 指标描述卡片 */}
      <View style={$styles.cardContainer}>
        {/* Left Module - Activity */}
        <View style={$styles.leftModule}>
          <Text style={$styles.moduleTitle1}>活动</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{fitnessData.activityCalories}</Text>
            <Text style={$styles.unitText}>kcal</Text>
          </View>
        </View>

        {/* Middle Module - Exercise */}
        <View style={$styles.middleModule}>
          <Text style={$styles.moduleTitle2}>锻炼</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{fitnessData.exerciseMinutes}</Text>
            <Text style={$styles.unitText}>分钟</Text>
          </View>
        </View>

        {/* Right Module - Stand */}
        <View style={$styles.rightModule}>
          <Text style={$styles.moduleTitle3}>站立</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{fitnessData.standHours}</Text>
            <Text style={$styles.unitText}>小时</Text>
          </View>
        </View>

        {/* 环形图-图表容器 */}
        <View style={$styles.chartContainer}>
          <SvgChart ref={chartRef} />
        </View>
      </View>
    </View>
  )
})

const createStyles = (
  theme: ReturnType<typeof useAppTheme>,
  $styleOverride?: ViewStyle
) => {
  return {
    container: {
      ...globalStyles.card,
      padding: aw(16),
      marginTop: aw(16),
      ...$styleOverride,
    } as ViewStyle,
    header: {
      marginBottom: ah(24),
    } as ViewStyle,
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    iconContainer: {
      borderRadius: aw(10),
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: aw(4),
    } as ViewStyle,
    title: {
      fontFamily: theme.theme.typography.primary.bold,
      fontSize: aw(14),
      lineHeight: aw(20),
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    chartContainer: {
      width: aw(50),
      height: aw(50),
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "#1B225C",
      borderRadius: aw(50),
    } as ViewStyle,

    cardContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    } as ViewStyle,

    leftModule: {
      flex: 1,
      alignItems: 'flex-start',
      marginRight: aw(16),
    } as ViewStyle,

    middleModule: {
      flex: 1,
      alignItems: 'flex-start',
      marginRight: aw(16),
    } as ViewStyle,

    rightModule: {
      flex: 1,
      alignItems: 'flex-start',
    } as ViewStyle,

    moduleTitle1: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: '#EA5E70',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    moduleTitle2: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: '#5CCBDA',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    moduleTitle3: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: '#699AE5',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    valueContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    valueText: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
    } as ViewStyle,

    unitText: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.textDim,
      marginLeft: aw(4),
    } as ViewStyle,

    targetText: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
    } as ViewStyle,

    infoIcon: {
      marginLeft: aw(4),
    } as ImageStyle,

    activityIcon: {
      top: aw(2),
    } as ImageStyle,

    exerciseIcon: {
      top: aw(10),
    } as ImageStyle,

    standIcon: {
      top: aw(18),
    } as ImageStyle,
  }
}