import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils"
import { useMemo, useEffect, useRef } from "react"
import { aw, ah } from "@/utils"
import { $styles as globalStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"

export interface SleepCardProps {
  duration?: number
  style?: ViewStyle
  sleepStagesData?: Array<{
    stage: string
    startTime: string
    endTime: string
  }>
}

/**
 * 睡眠卡片组件
 */
export const SleepCard = observer(function SleepCard({
  duration = 405, // 默认6小时45分钟，单位分钟
  style: $styleOverride,
  sleepStagesData = [],
}: SleepCardProps) {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)
  const $styles = useMemo(() => createStyles(theme, $styleOverride), [theme, $styleOverride])

  // 计算小时和分钟
  const hours = Math.floor(duration / 60)
  const minutes = duration % 60

  // 睡眠时间轴数据
  const sleepTimelineData = useMemo(() => {
    if (!sleepStagesData) {
      return []
    }
    const data: Array<{
      name: string
      value: [number, number, number, string]
      itemStyle: { color: string }
    }> = []

    const yAxisData = ['深睡', '浅睡', 'REM', '清醒']
    const stageColorMapping: { [key: string]: string } = {
      深睡: "#AB81FF",
      浅睡: "#D8C8FE",
      REM: "#F1E9FE",
      清醒: "#FDEFA6",
    }

    sleepStagesData.forEach((stageInfo) => {
      const categoryIndex = yAxisData.indexOf(stageInfo.stage)
      const startTime = new Date(stageInfo.startTime).getTime()
      const endTime = new Date(stageInfo.endTime).getTime()

      if (categoryIndex !== -1) {
        data.push({
          name: stageInfo.stage,
          value: [categoryIndex, startTime, endTime, stageInfo.stage],
          itemStyle: { color: stageColorMapping[stageInfo.stage] },
        })
      }
    })

    // 按开始时间对数据进行排序
    data.sort((a, b) => a.value[1] - b.value[1])

    return data
  }, [sleepStagesData])

  // ECharts 配置
  const chartOption = useMemo(() => {
    if (sleepTimelineData.length === 0) {
      return {}
    }
    const minTime = new Date(sleepTimelineData[0].value[1])
    minTime.setMinutes(minTime.getMinutes() - 15)
    const maxTime = new Date(sleepTimelineData[sleepTimelineData.length - 1].value[2])
    maxTime.setMinutes(maxTime.getMinutes() + 15)
    return {
      tooltip: {
        formatter: (params: any) => {
          const data = params.data
          const startTime = new Date(data.value[1])
          const endTime = new Date(data.value[2])
          return `${data.value[3]} ${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}~${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`
        }
      },
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        // height: '60%'
      },
      xAxis: {
        type: 'time',
        min: minTime.getTime(),
        max: maxTime.getTime(),
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'category',
        data: ['深睡', '浅睡', 'REM', '清醒'],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false }
      },
      series: [{
        type: 'custom',
        renderItem: (params: any, api: any) => {
          const categoryIndex = api.value(0)
          const start = api.coord([api.value(1), categoryIndex])
          const end = api.coord([api.value(2), categoryIndex])
          const height = api.size([0, 1])[1]
          const color = api.visual('color')

          return {
            type: 'rect',
            shape: {
              x: start[0],
              y: start[1] - height / 2,
              width: end[0] - start[0],
              height: height,
            },
            style: {
              fill: color,
              stroke: 'transparent',
              lineWidth: 0
            }
          }
        },
        data: sleepTimelineData
      }]
    }
  }, [sleepTimelineData])

  // 图表初始化
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      chartInstance = echarts.init(chartRef.current, "light", {
        renderer: "svg",
        width: aw(180),
        height: ah(60),
      })
      chartInstance.setOption(chartOption)
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [chartOption])


  return (
    <View style={$styles.container}>
      <View style={$styles.header}>
        <View style={$styles.titleContainer}>
          <View style={$styles.iconContainer}>
            <Icon icon="sleepEfficiency" size={aw(16)} useOriginalColor />
          </View>
          <Text style={$styles.title} text="睡眠" />
        </View>
      </View>

      <View style={$styles.content}>
        <View style={$styles.textContainer}>
          <Text style={$styles.durationLabel} text="时长" />
          <View style={$styles.timeContainer}>
            <Text style={$styles.timeValue} text={hours.toString()} />
            <Text style={$styles.timeUnit} text="时" />
            <Text style={$styles.timeValue} text={minutes.toString().padStart(2, '0')} />
            <Text style={$styles.timeUnit} text="分" />
          </View>
        </View>

        <View style={$styles.chartContainer}>
          <SvgChart ref={chartRef} />
        </View>
      </View>
    </View>
  )
})

const createStyles = (
  theme: ReturnType<typeof useAppTheme>,
  $styleOverride?: ViewStyle
) => {
  return {
    container: {
      ...globalStyles.card,
      padding: aw(16),
      ...$styleOverride,
    } as ViewStyle,
    header: {
      marginBottom: ah(24),
    } as ViewStyle,
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    iconContainer: {
      borderRadius: aw(10),
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: aw(4),
    } as ViewStyle,
    title: {
      fontFamily: theme.theme.typography.primary.bold,
      fontSize: aw(14),
      lineHeight: aw(20),
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    content: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    } as ViewStyle,
    textContainer: {
      flex: 1,
    } as ViewStyle,
    durationLabel: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: ah(8),
    } as TextStyle,
    timeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    timeValue: {
      fontFamily: theme.theme.typography.primary.bold,
      fontSize: aw(24),
      lineHeight: aw(26),
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    timeUnit: {
      fontFamily: theme.theme.typography.primary.medium,
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary100,
      marginHorizontal: aw(4),
    } as TextStyle,
    chartContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
      overflow: 'hidden', // 防止内容溢出
    } as ViewStyle,
  }
}
