import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, ImageStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils"
import { useMemo, useEffect, useRef } from "react"
import { aw, ah } from "@/utils"
import { $styles as globalStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"


interface HeartRateCardProps {
  heartRateData: { time: string; value: number }[]
}


const CHART_HEIGHT = aw(50)
const CHART_WIDTH = aw(134)
/**
 * 心率卡片组件
 */
export const HeartRate = observer(function HeartRate({ heartRateData }: HeartRateCardProps) {
  console.log("🚀 ~ HeartRate ~ heartRateData:", heartRateData.length)
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)
  const chartInstanceRef = useRef<ECharts | null>(null)
  const $styles = useMemo(() => createStyles(theme), [theme])

  // 处理图表数据
  const chartData = useMemo(() => {
    return heartRateData.map((item) => ({ time: new Date(item.time).getTime(), value: item.value }))
  }, [heartRateData])

  // 计算心率指标
  const heartRateMetrics = useMemo(() => {
    if (heartRateData.length === 0) {
      return {
        latest: "--",
        average: "--",
      }
    }

    const values = heartRateData.map((item) => item.value)
    const latest = values[values.length - 1]
    const average = Math.round(values.reduce((sum, val) => sum + val, 0) / values.length)

    return {
      latest: latest.toString(),
      average: average.toString(),
    }
  }, [heartRateData])

  // ECharts 配置
  const chartOption = useMemo(() => {
    if (chartData.length === 0) {
      return {}
    }
    return {
      grid: {
        left: 0,
        right: 0,
        bottom: aw(20),
      },
      xAxis: {
        type: "time",
        show: false,
      },
      yAxis: {
        show: false,
        type: "value",
      },
      series: [
        {
          name: "心率",
          type: "line",
          smooth: true,
          symbol: "none",
          lineStyle: {
            color: "#F76B8A",
            width: aw(3),
          },
          data: chartData.map((item) => [item.time, item.value]),
        },
      ],
    }
  }, [chartData])

  // 图表初始化和销毁
  useEffect(() => {
    let chart: ECharts | undefined
    if (chartRef.current) {
      try {
        chart = echarts.init(chartRef.current, "light", {
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstanceRef.current = chart
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }
    }

    return () => {
      if (chart) {
        chart.dispose()
        chartInstanceRef.current = null
      }
    }
  }, []) // 仅在挂载和卸载时运行

  // 更新图表选项
  useEffect(() => {
    if (chartInstanceRef.current && Object.keys(chartOption).length > 0) {
      try {
        chartInstanceRef.current.setOption(chartOption)
      } catch (error) {
        console.error("ECharts setOption error:", error)
      }
    }
  }, [chartOption]) // 当选项更改时运行



  return (
    <View style={$styles.container}>
      <View style={$styles.header}>
        <View style={$styles.titleContainer}>
          <View style={$styles.iconContainer}>
            <Icon icon="restingHeartRate" size={aw(16)} useOriginalColor />
          </View>
          <Text style={$styles.title} text="心率" />
        </View>
      </View>


      {/* 指标描述卡片 */}
      <View style={$styles.cardContainer}>
        {/* Left Module - Activity */}
        <View style={$styles.leftModule}>
          <Text style={$styles.moduleTitle1}>最新</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{heartRateMetrics.latest}</Text>
            <Text style={$styles.unitText}>/bpm</Text>
          </View>
        </View>

        {/* Middle Module - Exercise */}
        <View style={$styles.middleModule}>
          <Text style={$styles.moduleTitle2}>平均</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{heartRateMetrics.average}</Text>
            <Text style={$styles.unitText}>/bpm</Text>
          </View>
        </View>

        {/* Right Module - Stand */}
        {/* <View style={$styles.rightModule}>
        </View> */}

        {/* 环形图-图表容器 */}
        <View style={$styles.chartContainer}>
          <SvgChart ref={chartRef} />
        </View>
      </View>
    </View>
  )
})

const createStyles = (
  theme: ReturnType<typeof useAppTheme>,
  $styleOverride?: ViewStyle
) => {
  return {
    container: {
      ...globalStyles.card,
      padding: aw(16),
      marginTop: aw(16),
      ...$styleOverride,
    } as ViewStyle,
    header: {
      marginBottom: ah(24),
    } as ViewStyle,
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,
    iconContainer: {
      borderRadius: aw(10),
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: aw(4),
    } as ViewStyle,
    title: {
      fontFamily: theme.theme.typography.primary.bold,
      fontSize: aw(14),
      lineHeight: aw(20),
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,
    chartContainer: {
      width: CHART_WIDTH,
      height: CHART_HEIGHT,
      alignItems: "center",
      justifyContent: "center",
    } as ViewStyle,

    cardContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    } as ViewStyle,

    leftModule: {
      flex: 1,
      alignItems: 'flex-start',
      marginRight: aw(16),
    } as ViewStyle,

    middleModule: {
      flex: 1,
      alignItems: 'flex-start',
      marginRight: aw(16),
    } as ViewStyle,

    rightModule: {
      flex: 1,
      alignItems: 'flex-start',
    } as ViewStyle,

    moduleTitle1: {
      color: theme.theme.colors.palette.primary200,
      fontSize: aw(12),
      lineHeight: aw(16),
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    moduleTitle2: {
      color: theme.theme.colors.palette.primary200,
      fontSize: aw(12),
      lineHeight: aw(16),
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    moduleTitle3: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: '#699AE5',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    valueContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    valueText: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
    } as ViewStyle,

    unitText: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.textDim,
      marginLeft: aw(4),
    } as ViewStyle,

    targetText: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
    } as ViewStyle,

    infoIcon: {
      marginLeft: aw(4),
    } as ImageStyle,

    activityIcon: {
      top: aw(2),
    } as ImageStyle,

    exerciseIcon: {
      top: aw(10),
    } as ImageStyle,

    standIcon: {
      top: aw(18),
    } as ImageStyle,
  }
}