import { useState } from "react"
import { View, ViewStyle, Image, ImageStyle, TextStyle, Linking } from "react-native"
import { observer } from "mobx-react-lite"
import { Screen, Text, Button, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { useStores } from "@/models"
import { AppStackScreenProps } from "@/navigators/AppNavigator"
import { colors, spacing } from "@/theme"
import { aw } from "@/utils/adaptiveSize"
import { Header } from "@/components"
import { useMemo } from "react"

export const LoginScreen = observer(function LoginScreen({
  navigation,
}: AppStackScreenProps<"LoginScreen">) {
  const { authStore } = useStores()
  const theme = useAppTheme()
  const [isLoading, setIsLoading] = useState(false)

  const handleAppleLogin = async () => {
    try {
      setIsLoading(true)
      const result = await authStore.signInWithApple()
      if (result.success) {
        // 登录成功，返回上一页或导航到首页
        navigation.goBack()
      } else {
        // 处理登录失败
        console.log("Apple 登录失败:", result.error)
      }
    } catch (error) {
      console.error("登录过程中出错:", error)
    } finally {
      setIsLoading(false)
    }
  }
  const $styles = useMemo(() => createStyles(theme), [theme])



  return (
    <>
      <Header
        title="登录"
        leftIcon="caretLeft"
        onLeftPress={() => navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />
      <Screen
        preset="auto"
        contentContainerStyle={$styles.screen}
        safeAreaEdges={["bottom"]}
      >
        <View style={$styles.container}>
          <View style={$styles.header}>
            <View style={$styles.logoContainer}>
              <Image
                source={require("assets/images/calm-img.png")}
                style={$styles.logo}
                resizeMode="contain"
              />
            </View>
            <Text style={$styles.title}>CalmWatch</Text>
            <Text style={$styles.subtitle}>感知身体压力，开启健康之旅</Text>
          </View>

          <View style={$styles.buttonContainer}>
            <Button
              preset="default"
              style={$styles.appleButton}
              onPress={handleAppleLogin}
              disabled={isLoading}
            >
              <Text style={$styles.appleButtonText}>
                {isLoading ? '登录中...' : 'Apple 登录'}
              </Text>
            </Button>
            <View style={$styles.footer}>
              <Text style={$styles.footerText}>
                点击按钮表示您同意我们的
                <Text style={$styles.linkText} onPress={() => Linking.openURL('https://wathch.zoss.cc/privacyPolicy')}>
                  隐私协议
                </Text>
                和
                <Text style={$styles.linkText} onPress={() => Linking.openURL('https://wathch.zoss.cc/termsService')}>
                  服务条款
                </Text>
              </Text>
            </View>
          </View>
        </View>
      </Screen>
    </>

  )
})
const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screen: {
      flex: 1,
      backgroundColor: theme.theme.colors.background,
      paddingHorizontal: spacing.xl,
      justifyContent: 'space-between',
      paddingVertical: aw(42),
    } as ViewStyle,
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
    } as ViewStyle,
    header: {
      alignItems: 'center',
      marginTop: aw(123),
    } as ViewStyle,
    logoContainer: {
      position: 'relative',
      marginBottom: spacing.xl,
    } as ViewStyle,
    logo: {
      width: aw(184),
      height: aw(184),
    } as ImageStyle,
    starDecoration: {
      position: 'absolute',
      width: aw(24),
      height: aw(24),
      tintColor: theme.theme.colors.palette.secondary500,
    } as ImageStyle,
    title: {
      fontSize: aw(36),
      lineHeight: aw(40),
      fontWeight: 'bold',
      color: theme.theme.colors.text,
      fontFamily: 'PingFang SC, PingFang SC',
      marginBottom: aw(8),
      textAlign: 'center',
    } as TextStyle,
    subtitle: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary200,
      textAlign: 'center',
      lineHeight: aw(16),
      maxWidth: '80%',
    } as TextStyle,
    buttonContainer: {
      width: '100%',
      alignItems: 'center',
      marginTop: spacing.xxl,
    } as ViewStyle,
    appleButton: {
      width: '100%',
      height: aw(50),
      borderRadius: aw(32),
      backgroundColor: '#00C785',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    } as ViewStyle,
    appleButtonText: {
      color: colors.palette.neutral100,
      fontSize: aw(16),
      fontWeight: '600',
      marginLeft: spacing.sm,
      fontFamily: 'System',
    } as TextStyle,
    footer: {
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
      marginTop: aw(8),
    } as ViewStyle,
    footerText: {
      color: theme.theme.colors.palette.primary200,
      fontSize: aw(12),
      textAlign: 'center',
      lineHeight: aw(18),
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
    } as TextStyle,
    linkText: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary500,
      textDecorationLine: 'underline',
      marginHorizontal: 4,
    } as TextStyle,
  }
}