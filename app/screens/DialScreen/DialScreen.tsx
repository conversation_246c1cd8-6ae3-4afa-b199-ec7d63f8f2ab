import { observer } from "mobx-react-lite"
import { Screen, Header, Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { useMemo } from "react"
import { View, ViewStyle, Image, ImageStyle, TouchableOpacity, TextStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators/AppNavigator"
import { aw } from "@/utils/adaptiveSize"
import { $styles as globalStyles } from "@/theme/styles"
// 定义导航参数类型
type DialScreenProps = AppStackScreenProps<"DialScreen">

export const DialScreen = observer(function DialScreen({ navigation }: DialScreenProps) {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])

  return (
    <>
      <Header
        title="表盘组件"
        leftIcon="caretLeft"
        onLeftPress={() => navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />
      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >
        <View style={$styles.container}>
          <Text style={$styles.sectionTitle}>常规组件</Text>
          <View style={$styles.cardsContainer}>
            {/* 第一个表盘组件 */}
            <View style={$styles.card}>
              <Image
                source={require("assets/images/logo.png")}
                style={$styles.watchImage}
                resizeMode="contain"
              />
              <TouchableOpacity style={$styles.addButton}>
                <Text style={$styles.addButtonText}>添加</Text>
              </TouchableOpacity>
            </View>

            {/* 第二个表盘组件 */}
            <View style={$styles.card}>
              <Image
                source={require("assets/images/logo.png")}
                style={$styles.watchImage}
                resizeMode="contain"
              />
              <TouchableOpacity style={$styles.addButton}>
                <Text style={$styles.addButtonText}>添加</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Screen>
    </>

  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      flex: 1,
      paddingHorizontal: aw(16),
      backgroundColor: theme.theme.colors.background,
    } as ViewStyle,
    container: {
      flex: 1,
    } as ViewStyle,
    sectionTitle: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
      marginBottom: aw(8),
      marginLeft: aw(4),
    } as TextStyle,
    cardsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: aw(4),
    } as ViewStyle,
    card: {
      ...globalStyles.card,
      width: '48%',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      alignItems: 'center',
    } as ViewStyle,
    watchImage: {
      width: '100%',
      height: aw(120),
      marginBottom: aw(16),
    } as ImageStyle,
    addButton: {
      width: '100%',
      backgroundColor: '#00C785',
      paddingHorizontal: aw(20),
      paddingVertical: aw(8),
      borderRadius: aw(20),
      minWidth: aw(80),
      alignItems: 'center',
    } as ViewStyle,
    addButtonText: {
      color: '#fff',
      fontSize: aw(14),
      fontWeight: '500',
    } as TextStyle,
  }
}
