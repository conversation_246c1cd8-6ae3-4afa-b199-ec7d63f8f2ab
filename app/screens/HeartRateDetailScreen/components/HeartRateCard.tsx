import { FC, useRef, useEffect, useMemo } from "react"
import { View, Dimensions, ViewStyle } from "react-native"
import { aw, ah } from "@/utils/adaptiveSize"
import { Text } from "@/components"
import { InfoIcon } from "@/components/InfoIcon"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"

const screenWidth = Dimensions.get("window").width
const CHART_HEIGHT = ah(150)
const CHART_WIDTH = screenWidth - aw(64)

interface HeartRateCardProps {
  heartRateData: { time: string; value: number }[]
}

export const HeartRateCard: FC<HeartRateCardProps> = ({ heartRateData }) => {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)
  const chartRef = useRef<any>(null)

  // 处理图表数据
  const chartData = useMemo(() => {
    return heartRateData.map(item => ({ time: new Date(item.time).getTime(), value: item.value }))
  }, [heartRateData])

  // 计算心率指标
  const heartRateMetrics = useMemo(() => {
    if (heartRateData.length === 0) {
      return {
        latest: '--',
        average: '--',
        max: '--',
        min: '--'
      }
    }

    const values = heartRateData.map(item => item.value)
    const latest = values[values.length - 1]
    const average = Math.round(values.reduce((sum, val) => sum + val, 0) / values.length)
    const max = Math.max(...values)
    const min = Math.min(...values)

    return {
      latest: latest.toString(),
      average: average.toString(),
      max: max.toString(),
      min: min.toString()
    }
  }, [heartRateData])

  // ECharts 配置
  const chartOption = useMemo(() => {
    if (chartData.length === 0) {
      return {}
    }
    return {
      // backgroundColor: '#FFFFFF',
      tooltip: {
        trigger: 'axis',
        confine: true,
        borderWidth: 1,
        borderColor: '#f0f0f0',
        formatter: (params: any) => {
          const param = params[0]
          const time = new Date(param.value[0]).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })
          const value = param.value[1]
          return `时间：${time}\n心率: ${value} bpm`
        },
      },
      grid: {
        left: aw(16),
        right: aw(30),
        top: aw(10),
        bottom: aw(16),
      },
      xAxis: {
        type: 'time',
        show: false,
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 200,
        interval: 100,
        position: 'right',
        axisLabel: {
          color: '#BCBFD0',
          fontSize: aw(12),
          formatter: '{value}'
        },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E5E5E5',
            width: 1
          }
        }
      },
      series: [{
        name: '心率',
        type: 'line',
        smooth: false,
        symbol: 'none',
        lineStyle: {
          color: '#F76B8A',
          width: aw(1),
        },
        data: chartData.map(item => [item.time, item.value])
      }]
    }
  }, [chartData])

  // 图表初始化
  useEffect(() => {
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      let chartInstance: ECharts | undefined
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }

      return () => {
        if (chartInstance) {
          chartInstance.dispose()
        }
      }
    }
    return undefined
  }, [chartOption])

  const $styles = {
    container: {
      ...globalStyles.card,
      marginBottom: ah(theme.theme.spacing.md),
    } as ViewStyle,

    summaryContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: ah(16)
    } as ViewStyle,

    chartContainer: {
      height: CHART_HEIGHT,
      alignItems: "center",
    } as ViewStyle,

    timeContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: aw(16),
    } as ViewStyle,

    timeText: {
      fontSize: aw(12),
      color: '#BCBFD0',
    } as ViewStyle,

    metricsContainer: {
      marginTop: ah(16),
    } as ViewStyle,

    metricsRow1: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: ah(12),
    } as ViewStyle,

    metricsRow2: {
      flexDirection: "row",
      justifyContent: "space-between",
    } as ViewStyle,

    metricCard: {
      flex: 1,
      backgroundColor: theme.theme.colors.palette.neutral200,
      borderRadius: aw(12),
      paddingVertical: ah(16),
      paddingHorizontal: aw(12),
      marginHorizontal: aw(6),
      alignItems: "flex-start",
      justifyContent: "center",
    } as ViewStyle,

    metricLabel: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary200,
      marginBottom: aw(16),
    } as ViewStyle,

    metricValueContainer: {
      flexDirection: "row",
      alignItems: "baseline",
    } as ViewStyle,

    metricValue: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary500,
    } as ViewStyle,

    metricUnit: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary100,
      marginLeft: aw(4),
    } as ViewStyle,
  }

  return (
    <View style={$styles.container}>
      <View style={$styles.summaryContainer}>
        <Text style={[themedStyles.sectionTitle, { marginRight: aw(4) }]} preset="subheading" text="心率" />
        <InfoIcon icon="info" size={aw(16)} color={'#BCBFD0'} title={'心率'} content={'暂无指标说明'} />
      </View>

      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>

      <View style={$styles.timeContainer}>
        <Text style={$styles.timeText} text={heartRateData[0]?.time ? new Date(heartRateData[0].time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false }) : ''} />
        <Text style={$styles.timeText} text={heartRateData[heartRateData.length - 1]?.time ? new Date(heartRateData[heartRateData.length - 1].time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false }) : ''} />
      </View>

      {/* 指标展示 */}
      <View style={$styles.metricsContainer}>
        <View style={$styles.metricsRow1}>
          <View style={$styles.metricCard}>
            <Text style={$styles.metricLabel} text="最新" />
            <View style={$styles.metricValueContainer}>
              <Text style={$styles.metricValue} text={heartRateMetrics.latest} />
              <Text style={$styles.metricUnit} text="bpm" />
            </View>
          </View>
          <View style={$styles.metricCard}>
            <Text style={$styles.metricLabel} text="平均" />
            <View style={$styles.metricValueContainer}>
              <Text style={$styles.metricValue} text={heartRateMetrics.average} />
              <Text style={$styles.metricUnit} text="bpm" />
            </View>
          </View>
        </View>
        <View style={$styles.metricsRow2}>
          <View style={$styles.metricCard}>
            <Text style={$styles.metricLabel} text="最高心率" />
            <View style={$styles.metricValueContainer}>
              <Text style={$styles.metricValue} text={heartRateMetrics.max} />
              <Text style={$styles.metricUnit} text="bpm" />
            </View>
          </View>
          <View style={$styles.metricCard}>
            <Text style={$styles.metricLabel} text="最低心率" />
            <View style={$styles.metricValueContainer}>
              <Text style={$styles.metricValue} text={heartRateMetrics.min} />
              <Text style={$styles.metricUnit} text="bpm" />
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}