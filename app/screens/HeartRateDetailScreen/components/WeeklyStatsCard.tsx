import { FC, useRef, useEffect, useMemo } from "react"
import { View, Dimensions, ViewStyle } from "react-native"
import { aw, ah } from "@/utils/adaptiveSize"
import { Text } from "@/components"

import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"

const screenWidth = Dimensions.get("window").width

const MetricItem: FC<{ title: string; value: string; unit: string }> = ({ title, value, unit }) => {
  const theme = useAppTheme()
  const $styles = {
    metricCard: {
      flex: 1,
      backgroundColor: theme.theme.colors.palette.neutral200,
      borderRadius: aw(16),
      padding: aw(16),
      alignItems: "flex-start",
      justifyContent: "center",
    } as ViewStyle,
    metricTitle: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary200,
      marginBottom: aw(16),
    } as ViewStyle,
    metricValueContainer: {
      flexDirection: "row",
      alignItems: "baseline",
    } as ViewStyle,
    metricValue: {
      fontSize: aw(24),
      lineHeight: ah(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary500,
    } as ViewStyle,
    metricUnit: {
      fontSize: aw(14),
      color: theme.theme.colors.palette.primary100,
      marginLeft: aw(4),
    } as ViewStyle,
  }

  return (
    <View style={$styles.metricCard}>
      <Text style={$styles.metricTitle}>{title}</Text>
      <View style={$styles.metricValueContainer}>
        <Text style={$styles.metricValue}>{value}</Text>
        <Text style={$styles.metricUnit}>{unit}</Text>
      </View>
    </View>
  )
}
const CHART_HEIGHT = ah(200)
const CHART_WIDTH = screenWidth - aw(64)

interface WeeklyStatsCardProps {
  weekHeartRateData: { date: string; min: number; max: number }[]
}

export const WeeklyStatsCard: FC<WeeklyStatsCardProps> = ({ weekHeartRateData }) => {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)
  const chartRef = useRef<any>(null)

  // 使用传入的周统计数据
  const weeklyData = useMemo(() => (weekHeartRateData || []).map(item => ({
    date: new Date(item.date).toLocaleDateString('en-US', { month: 'numeric', day: 'numeric' }),
    min: item.min,
    max: item.max,
  })), [weekHeartRateData])

  const { weeklyAverage, minRange, maxRange } = useMemo(() => {
    if (!weekHeartRateData || weekHeartRateData.length === 0) {
      return { weeklyAverage: 0, minRange: 0, maxRange: 0 }
    }

    const validData = weekHeartRateData.filter(d => d.min > 0 && d.max > 0)
    if (validData.length === 0) {
      return { weeklyAverage: 0, minRange: 0, maxRange: 0 }
    }

    const totalAverage = validData.reduce((sum, item) => sum + (item.min + item.max) / 2, 0)
    const weeklyAverage = Math.round(totalAverage / validData.length)

    const minRange = Math.min(...validData.map(item => item.min))
    const maxRange = Math.max(...validData.map(item => item.max))

    return { weeklyAverage, minRange, maxRange }
  }, [weekHeartRateData])

  // ECharts 柱状图配置
  const chartOption = useMemo(() => {
    return {
      backgroundColor: '#FFFFFF',
      tooltip: {
        trigger: 'axis',
        confine: true,
        borderWidth: 1,
        borderColor: '#f0f0f0',
        formatter: (params: any) => {
          const data = params[0].data
          const min = data[1]
          const max = data[2]
          const date = weeklyData[params[0].dataIndex].date
          if (min === 0 && max === 0) {
            return null
          }
          return `${date}\n${min}~${max} bpm`
        },
      },
      grid: {
        left: aw(16),
        right: aw(30),
        top: aw(20),
        bottom: aw(40),
      },
      xAxis: {
        type: 'category',
        data: weeklyData.map(item => item.date),
        axisLabel: {
          color: '#BCBFD0',
          fontSize: aw(12),
        },
        axisLine: { show: false },
        axisTick: { show: false },
        boundaryGap: true
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 160,
        interval: 80,
        position: 'right',
        axisLabel: {
          color: '#BCBFD0',
          fontSize: aw(12),
          formatter: '{value}'
        },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E5E5E5',
            width: 1
          }
        }
      },
      series: [{
        name: '心率范围',
        type: 'custom',
        renderItem: (params: any, api: any) => {
          const categoryIndex = api.value(0)
          const min = api.value(1)
          const max = api.value(2)

          if (min === 0 && max === 0) {
            return null
          }

          const start = api.coord([categoryIndex, min])
          const end = api.coord([categoryIndex, max])
          const barWidth = aw(16)

          const x = start[0] - barWidth / 2
          const y = end[1]
          const width = barWidth
          const height = start[1] - end[1]
          const radius = aw(8)

          // 创建圆角矩形路径
          const pathData = `M ${x + radius} ${y} ` +
            `L ${x + width - radius} ${y} ` +
            `Q ${x + width} ${y} ${x + width} ${y + radius} ` +
            `L ${x + width} ${y + height - radius} ` +
            `Q ${x + width} ${y + height} ${x + width - radius} ${y + height} ` +
            `L ${x + radius} ${y + height} ` +
            `Q ${x} ${y + height} ${x} ${y + height - radius} ` +
            `L ${x} ${y + radius} ` +
            `Q ${x} ${y} ${x + radius} ${y} Z`

          return {
            type: 'path',
            shape: {
              pathData: pathData
            },
            style: {
              fill: '#F76B8A'
            }
          }
        },
        data: weeklyData.map((item, index) => [index, item.min, item.max])
      }]
    }
  }, [weeklyData])

  // 图表初始化
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      chartInstance = echarts.init(chartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: CHART_HEIGHT,
      })
      chartInstance.setOption(chartOption)
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [chartOption])

  const $styles = {
    container: {
      ...globalStyles.card,
      marginBottom: ah(theme.theme.spacing.md),
    } as ViewStyle,

    summaryContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: ah(16)
    } as ViewStyle,

    chartContainer: {
      alignItems: "center",
      justifyContent: "center",
      height: CHART_HEIGHT,
      width: CHART_WIDTH,
    } as ViewStyle,

    metricsContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: ah(16),
      gap: aw(16),
    } as ViewStyle,
  }

  return (
    <View style={$styles.container}>
      <View style={$styles.summaryContainer}>
        <Text style={[themedStyles.sectionTitle, { marginRight: aw(4) }]} preset="subheading" text="周统计" />
      </View>

      {/* 统计图表 */}
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>
      {/* 指标说明 */}
      <View style={$styles.metricsContainer}>
        <MetricItem title="平均心率" value={String(weeklyAverage)} unit="bpm" />
        <MetricItem title="心率范围" value={`${minRange}~${maxRange}`} unit="bpm" />
      </View>
    </View>
  )
}