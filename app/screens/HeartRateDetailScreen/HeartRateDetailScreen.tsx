import { FC, useEffect, useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen, Header, DateHeader } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw } from "@/utils/adaptiveSize"
import { HeartRateCard, WeeklyStatsCard } from "./components"
import { mockData } from "@/data/stressData"
import { HeartRateData } from "@/data/types"

interface HeartRateDetailScreenProps extends AppStackScreenProps<"HeartRateDetailScreen"> { }

export const HeartRateDetailScreen: FC<HeartRateDetailScreenProps> = (_props) => {
  const theme = useAppTheme()
  const [heartRateData, setHeartRateData] = useState<HeartRateData>(mockData.heartRateData)

  useEffect(() => {
    // 在这里可以从API获取数据并更新状态
    setHeartRateData(mockData.heartRateData)
  }, [])
  const $styles = createStyle(theme)
  return (
    <>
      <Header
        title="心率"
        leftIcon="caretLeft"
        onLeftPress={() => _props.navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />
      {/* 日期 */}
      <DateHeader />

      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >
        {/* 心率卡片 */}
        <View style={$styles.container}>
          <HeartRateCard heartRateData={heartRateData.dayHeartRateData} />
          {/* 周统计 */}
          <WeeklyStatsCard weekHeartRateData={heartRateData.weekHeartRateData} />
        </View>
      </Screen>
    </>
  )
}

function createStyle(theme: ReturnType<typeof useAppTheme>) {
  return {
    screenContentContainer: {
      marginTop: aw(10),
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,
  }
}
