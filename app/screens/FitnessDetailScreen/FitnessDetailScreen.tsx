import { FC, useState } from "react"
import { View, ViewStyle } from "react-native"
import { <PERSON>, Header, DateHeader } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw } from "@/utils/adaptiveSize"
import { FitnessActivityChart, WeeklyActivityChart } from "./components"
import { mockData } from "@/data/stressData"

interface FitnessDetailScreenProps extends AppStackScreenProps<"FitnessDetailScreen"> { }

export const FitnessDetailScreen: FC<FitnessDetailScreenProps> = (_props) => {
  const theme = useAppTheme()
  const [fitnessData] = useState(mockData.fitnessData)
  const $styles = {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,


  }

  return (
    <>
      <Header
        title="健身"
        leftIcon="caretLeft"
        onLeftPress={() => _props.navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />
      {/* 日期 */}
      <DateHeader />

      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >
        <View style={$styles.container}>
          {/* 健身活动图表 */}
          <FitnessActivityChart
            fitnessData={fitnessData}
          />

          {/* 周活动卡片 */}
          <WeeklyActivityChart
            title="周活动"
            data={fitnessData.weeklyActivityData}
            color="#EA5E70"
          />

          {/* 周锻炼卡片 */}
          <WeeklyActivityChart
            title="周锻炼"
            data={fitnessData.weeklyExerciseData}
            color="#5CCBDA"
          />

          {/* 周站立卡片 */}
          <WeeklyActivityChart
            title="周站立"
            data={fitnessData.weeklyStandingData}
            color="#699AE5"
          />
        </View>
      </Screen>
    </>
  )
}