import { FC, useRef, useMemo, useEffect } from "react"
import { View, ViewStyle, Dimensions } from "react-native"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles } from "@/theme"
import { aw, af } from "@/utils/adaptiveSize"
import { Text } from "@/components"
import { formatDate } from "@/utils/formatDate"



interface WeeklyActivityData {
  date: string
  value: number
}

interface WeeklyActivityChartProps {
  title: string
  data: WeeklyActivityData[]
  color: string
}
const { width: screenWidth } = Dimensions.get('window')
const CHART_HEIGHT = aw(150)
const CHART_WIDTH = screenWidth - aw(64)

export const WeeklyActivityChart: FC<WeeklyActivityChartProps> = ({ title, data, color }) => {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)

  const chartOption = useMemo(() => {
    // 在组件内部处理日期格式化
    const dates = data.map(item => formatDate(item.date, "M/d"))
    const values = data.map(item => item.value)
    const maxValue = Math.max(...values)
    let yAxisMax = maxValue
    if (maxValue > 200) {
      yAxisMax = Math.ceil(maxValue / 100) * 100
    } else if (maxValue > 100) {
      yAxisMax = Math.ceil(maxValue / 50) * 50
    } else if (maxValue > 50) {
      yAxisMax = Math.ceil(maxValue / 25) * 25
    } else if (maxValue > 20) {
      yAxisMax = Math.ceil(maxValue / 10) * 10
    }

    return {
      grid: {
        left: 0,
        right: 0,
        top: aw(10),
        bottom: 0,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#BCBFD0',
          fontSize: aw(12),
          margin: aw(12),
        },
      },
      yAxis: {
        type: 'value',
        max: yAxisMax,
        min: 0,
        position: 'right',
        interval: yAxisMax / 2,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#BCBFD0',
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E5E5E5',
            type: 'dashed',
            width: 1,
          },
        },
      },
      series: [
        {
          type: 'bar',
          data: values,
          barWidth: aw(16),
          itemStyle: {
            color: color,
            borderRadius: [aw(8), aw(8), 0, 0],
          },
          barGap: '30%',
        },
      ],
    }
  }, [data, color])

  useEffect(() => {
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      let chartInstance: ECharts | undefined
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          renderer: "svg",
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }

      return () => {
        if (chartInstance) {
          chartInstance.dispose()
        }
      }
    }
    return undefined
  }, [chartOption])

  const $styles = {
    container: {
      ...globalStyles.card,
      marginBottom: aw(16),
      paddingVertical: aw(16),
      paddingHorizontal: aw(16),
    } as ViewStyle,

    titleText: {
      fontSize: af(16),
      lineHeight: aw(18),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary600,
      marginBottom: aw(16),
    } as ViewStyle,

    chartContainer: {
      position: 'relative',
      alignItems: "center",
      justifyContent: "center",
      height: CHART_HEIGHT,
      width: '100%',
    } as ViewStyle,
  }

  return (
    <View style={$styles.container}>
      <Text style={$styles.titleText}>{title}</Text>
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>
    </View>
  )
}