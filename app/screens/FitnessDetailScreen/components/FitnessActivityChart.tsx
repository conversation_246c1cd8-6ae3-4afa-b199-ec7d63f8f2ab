import { FC, useRef, useMemo, useEffect } from "react"
import { View, ViewStyle, ImageStyle } from "react-native"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles } from "@/theme"
import { aw, af } from "@/utils/adaptiveSize"
import { Text } from "@/components"



interface FitnessData {
  activityCalories: number
  targetActivityCalories: number
  exerciseMinutes: number
  targetExerciseMinutes: number
  standHours: number
  targetStandHours: number
}

interface FitnessActivityChartProps {
  fitnessData: FitnessData
}

const CHART_HEIGHT = 208
const CHART_WIDTH = 208

export const FitnessActivityChart: FC<FitnessActivityChartProps> = ({ fitnessData }) => {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)

  const chartOption = useMemo(() => {
    const activityPercentage = Math.min((fitnessData.activityCalories / fitnessData.targetActivityCalories) * 100, 100)
    const exercisePercentage = Math.min((fitnessData.exerciseMinutes / fitnessData.targetExerciseMinutes) * 100, 100)
    const standPercentage = Math.min((fitnessData.standHours / fitnessData.targetStandHours) * 100, 100)

    return {
      series: [
        {
          type: "gauge",
          radius: "100%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(20),
            roundCap: true,
            itemStyle: {
              color: "#EA5E70", // Red for activity
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(20),
              color: [[1, '#fff']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: activityPercentage }],
        },
        {
          type: "gauge",
          radius: "77%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(20),
            roundCap: true,
            itemStyle: {
              color: "#5CCBDA", // Teal for exercise
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(20),
              color: [[1, '#fff']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: exercisePercentage }],
        },
        {
          type: "gauge",
          radius: "54%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(20),
            roundCap: true,
            itemStyle: {
              color: "#699AE5", // Blue for stand
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(20),
              color: [[1, '#fff']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: standPercentage }],
        },
      ],
    }
  }, [fitnessData])

  useEffect(() => {
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      let chartInstance: ECharts | undefined
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          renderer: "svg",
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }

      return () => {
        if (chartInstance) {
          chartInstance.dispose()
        }
      }
    }
    return undefined
  }, [chartOption])

  const $styles = {
    container: {
      backgroundColor: theme.theme.colors.palette.neutral100,
      borderRadius: aw(12),
      padding: aw(16),
      marginBottom: aw(16),
    } as ViewStyle,

    chartContainer: {
      alignItems: "center",
      justifyContent: "center",
      height: CHART_HEIGHT,
      width: "100%",
      marginTop: aw(24),
    } as ViewStyle,

    cardContainer: {
      ...globalStyles.card,
      marginTop: aw(24),
      marginBottom: aw(16),
      paddingVertical: aw(16),
      paddingHorizontal: aw(24),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    } as ViewStyle,

    leftModule: {
      flex: 1,
      alignItems: 'flex-start',
      marginRight: aw(16),
    } as ViewStyle,

    middleModule: {
      flex: 1,
      alignItems: 'flex-start',
      marginRight: aw(16),
    } as ViewStyle,

    rightModule: {
      flex: 1,
      alignItems: 'flex-start',
    } as ViewStyle,

    moduleTitle1: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: '#EA5E70',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    moduleTitle2: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: '#5CCBDA',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    moduleTitle3: {
      fontSize: aw(14),
      lineHeight: aw(16),
      color: '#699AE5',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(8),
    } as ViewStyle,

    valueContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    valueText: {
      fontSize: af(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
    } as ViewStyle,

    unitText: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.textDim,
      marginLeft: aw(4),
    } as ViewStyle,

    targetText: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
    } as ViewStyle,

    infoIcon: {
      marginLeft: aw(4),
    } as ImageStyle,

    iconContainer: {
      position: "absolute",
      top: 0,
      left: aw(4),
      right: 0,
      bottom: 0,
      justifyContent: "flex-start",
      alignItems: "center",
      zIndex: 1,
      pointerEvents: "none",
      height: CHART_HEIGHT,
      width: '100%',
    } as ViewStyle,

    activityIcon: {
      top: aw(2),
    } as ImageStyle,

    exerciseIcon: {
      top: aw(10),
    } as ImageStyle,

    standIcon: {
      top: aw(18),
    } as ImageStyle,
  }

  return (
    <>
      {/* 环形图-图表容器 */}
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>

      {/* 指标描述卡片 */}
      <View style={$styles.cardContainer}>
        {/* Left Module - Activity */}
        <View style={$styles.leftModule}>
          <Text style={$styles.moduleTitle1}>活动</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{fitnessData.activityCalories}</Text>
            <Text style={$styles.unitText}>kcal</Text>
          </View>
        </View>

        {/* Middle Module - Exercise */}
        <View style={$styles.middleModule}>
          <Text style={$styles.moduleTitle2}>锻炼</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{fitnessData.exerciseMinutes}</Text>
            <Text style={$styles.unitText}>分钟</Text>
          </View>
        </View>

        {/* Right Module - Stand */}
        <View style={$styles.rightModule}>
          <Text style={$styles.moduleTitle3}>站立</Text>
          <View style={$styles.valueContainer}>
            <Text style={$styles.valueText}>{fitnessData.standHours}</Text>
            <Text style={$styles.unitText}>小时</Text>
          </View>
        </View>
      </View>
    </>
  )
}