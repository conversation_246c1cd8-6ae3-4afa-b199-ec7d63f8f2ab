import { FC, useMemo, useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen, Header, DateHeader } from "@/components"
import { SleepStagesCard, SleepDurationQualityChart, SleepHeartRateCard, SleepVitalSignsCard } from "./components"
import { mockData } from "@/data/stressData"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw } from "@/utils/adaptiveSize"

interface SleepDetailScreenProps extends AppStackScreenProps<"SleepDetailScreen"> { }

export const SleepDetailScreen: FC<SleepDetailScreenProps> = (_props) => {
  const theme = useAppTheme()

  // 将睡眠阶段数据转换为state，以便检测变化
  const [sleepStagesData] = useState(mockData.sleepData.sleepStagesData)

  // 计算睡眠阶段统计数据
  const processedSleepSummary = useMemo(() => {

    // 计算各阶段总时长
    const stageStats: { [key: string]: number } = {
      深睡: 0,
      浅睡: 0,
      REM: 0,
      清醒: 0,
    }

    let totalSleepTime = 0

    // 计算清醒次数
    let awakeCount = 0
    let previousStage = ''

    sleepStagesData.forEach(stage => {
      const duration = stage.duration
      if (stageStats.hasOwnProperty(stage.stage)) {
        stageStats[stage.stage] += duration
      }
      // 计算清醒次数（连续的清醒阶段算作一次）
      if (stage.stage === '清醒' && previousStage !== '清醒') {
        awakeCount++
      }
      previousStage = stage.stage

      // 只计算睡眠时间，不包括清醒时间
      if (stage.stage !== '清醒') {
        totalSleepTime += duration
      }
    })

    // 格式化时间显示
    const formatDuration = (minutes: number) => {
      const hours = Math.floor(minutes / 60)
      const mins = Math.round(minutes % 60)
      return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`
    }

    // 计算百分比并格式化数据
    return [
      {
        name: '深度睡眠',
        value: formatDuration(stageStats['深睡']),
        percentage: Math.round((stageStats['深睡'] / totalSleepTime) * 100)
      },
      {
        name: '核心睡眠',
        value: formatDuration(stageStats['浅睡']),
        percentage: Math.round((stageStats['浅睡'] / totalSleepTime) * 100)
      },
      {
        name: '快速眼动',
        value: formatDuration(stageStats['REM']),
        percentage: Math.round((stageStats['REM'] / totalSleepTime) * 100)
      },
      {
        name: '清醒时长',
        value: `${awakeCount}次·${formatDuration(stageStats['清醒'])}`,
        percentage: Math.round((stageStats['清醒'] / (totalSleepTime + stageStats['清醒'])) * 100)
      }
    ]
  }, [sleepStagesData])

  const $styles = {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,
  }

  return (
    <>
      <Header
        title="今日睡眠详情"
        leftIcon="caretLeft"
        onLeftPress={() => _props.navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
        rightText="设置"
        onRightPress={() => _props.navigation.navigate('Demo' as never)}
      />
      {/* 日期 */}
      <DateHeader />

      <Screen
        preset="auto"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >
        <View style={$styles.container}>
          {/* 睡眠时长&睡眠质量 */}
          <SleepDurationQualityChart
            sleepData={{
              targetSleepTime: mockData.sleepData.targetSleepTime,
              todaySleepTime: mockData.sleepData.todaySleepTime,
              sleepEfficiency: mockData.sleepData.sleepEfficiency,
            }}
          />

          {/* 睡眠阶段详情 */}
          <SleepStagesCard
            todaySleepSummary={processedSleepSummary}
            sleepStagesData={mockData.sleepData.sleepStagesData}
          />

          {/* 睡眠时心率 */}
          <SleepHeartRateCard sleepHeartRateData={mockData.sleepData.sleepHeartRateData} />

          {/* 睡眠生命体征 */}
          <SleepVitalSignsCard data={mockData.sleepData.SleepVitalSignsData} />
        </View>
      </Screen>
    </>
  )
}