import { useEffect, useRef, useMemo } from "react"
import { View, ViewStyle, ImageStyle } from "react-native"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles } from "@/theme"
import { aw, af } from "@/utils/adaptiveSize"
import { Text, Icon, InfoIcon } from "@/components"



interface SleepData {
  targetSleepTime: number,
  todaySleepTime: number,
  sleepEfficiency: number,
  sleepScore?: number,
}

interface SleepDurationQualityChartProps {
  sleepData: SleepData
}

const CHART_HEIGHT = 208
const CHART_WIDTH = 208

export const SleepDurationQualityChart: React.FC<SleepDurationQualityChartProps> = ({ sleepData }) => {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)

  // 计算睡眠质量等级
  const getSleepQualityLevel = (efficiency: number, score: number) => {
    if (efficiency >= 90 && score >= 85) return "优秀"
    if (efficiency >= 80 && score >= 70) return "良好"
    if (efficiency >= 70 && score >= 60) return "一般"
    return "较差"
  }

  const chartOption = useMemo(() => {
    const sleepDurationPercentage = sleepData.todaySleepTime / sleepData.targetSleepTime * 100
    const sleepQualityPercentage = sleepData.sleepEfficiency

    return {
      series: [
        {
          type: "gauge",
          radius: "100%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(20),
            roundCap: true,
            itemStyle: {
              color: "#9896F1", // Light purple
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(20),
              color: [[1, '#fff']],
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: sleepDurationPercentage }],
        },
        {
          type: "gauge",
          radius: "77%",
          center: ["50%", "50%"],
          startAngle: 90,
          endAngle: -270,
          min: 0,
          max: 100,
          progress: {
            show: true,
            width: aw(20),
            roundCap: true,
            itemStyle: {
              color: "#6EB6FF", // Light blue
              borderColor: '#fff',
              borderWidth: 1,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: aw(20),
              color: [[1, '#fff']],
            }
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false },
          data: [{ value: sleepQualityPercentage }],
        },
      ],
    }
  }, [sleepData, theme])

  useEffect(() => {
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      let chartInstance: ECharts | undefined
      try {
        chartInstance = echarts.init(chartRef.current, "light", {
          renderer: "svg",
          width: CHART_WIDTH,
          height: CHART_HEIGHT,
        })
        chartInstance.setOption(chartOption)
      } catch (error) {
        console.error("ECharts initialization error:", error)
      }

      return () => {
        if (chartInstance) {
          chartInstance.dispose()
        }
      }
    }
    return undefined
  }, [chartOption])

  const $styles = {
    container: {
      backgroundColor: theme.theme.colors.palette.neutral100,
      borderRadius: aw(12),
      padding: aw(16),
      marginBottom: aw(16),
    } as ViewStyle,

    chartContainer: {
      alignItems: "center",
      justifyContent: "center",
      height: CHART_HEIGHT,
      width: "100%",
      marginTop: aw(24),
    } as ViewStyle,

    summaryContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginTop: aw(16),
    } as ViewStyle,

    summaryItem: {
      alignItems: "center",
    } as ViewStyle,

    summaryValue: {
      fontSize: aw(18),
      fontWeight: "bold",
      color: theme.theme.colors.palette.primary500,
      marginBottom: aw(4),
    } as ViewStyle,

    summaryLabel: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.neutral600,
    } as ViewStyle,

    // New styles for the redesigned card
    cardContainer: {
      ...globalStyles.card,
      marginTop: aw(24),
      marginBottom: aw(16),
      paddingVertical: aw(16),
      paddingHorizontal: aw(24),
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    } as ViewStyle,

    leftModule: {
      alignItems: 'flex-start',
    } as ViewStyle,

    rightModule: {
      alignItems: 'flex-start',
    } as ViewStyle,

    moduleTitle1: {
      fontSize: aw(14),
      color: '#9896F1',
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: aw(16),
    } as ViewStyle,

    moduleTitle2: {
      fontSize: aw(14),
      color: '#6EB6FF',
      fontFamily: theme.theme.typography.primary.bold,
    } as ViewStyle,

    durationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: aw(8),
    } as ViewStyle,

    durationValue: {
      fontSize: af(24),
      lineHeight: aw(26),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary600,
    } as ViewStyle,

    durationUnit: {
      fontSize: aw(12),
      lineHeight: aw(24),
      color: theme.theme.colors.palette.primary100,
      marginHorizontal: aw(4),
    } as ViewStyle,

    targetText: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
    } as ViewStyle,

    qualityText: {
      fontSize: aw(20),
      lineHeight: aw(24),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary600,
      marginBottom: aw(8),
    } as ViewStyle,

    qualityPercentage: {
      fontSize: aw(12),
      lineHeight: aw(16),
      color: theme.theme.colors.palette.primary200,
    } as ViewStyle,

    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: aw(16),
    } as ViewStyle,

    infoIcon: {
      marginLeft: aw(4),
    } as ImageStyle,

    iconContainer: {
      position: "absolute",
      top: 0,
      left: aw(4),
      right: 0,
      bottom: 0,
      justifyContent: "flex-start",
      alignItems: "center",
      zIndex: 1,
      pointerEvents: "none",
      height: CHART_HEIGHT,
      width: '100%',
    } as ViewStyle,

    sleepTimeIcon: {
      top: aw(2),
    } as ImageStyle,

    sleepQualityIcon: {
      top: aw(10),
    } as ImageStyle,
  }

  return (
    <>
      {/* 环形图-图表容器 */}
      <View style={$styles.chartContainer}>
        <View style={$styles.iconContainer}>
          <Icon
            icon="sleepTime"
            size={aw(16)}
            useOriginalColor
            style={$styles.sleepTimeIcon}
          />
          <Icon
            icon="sleepEfficiency"
            size={aw(16)}
            useOriginalColor
            style={$styles.sleepQualityIcon}
          />
        </View>
        <SvgChart ref={chartRef} />
      </View>

      {/* 指标描述卡片 */}
      <View style={$styles.cardContainer}>
        {/* Left Module */}
        <View style={$styles.leftModule}>
          <Text style={$styles.moduleTitle1}>睡眠时长</Text>
          <View style={$styles.durationContainer}>
            <Text style={$styles.durationValue}>{Math.floor(sleepData.todaySleepTime / 60)}</Text>
            <Text style={$styles.durationUnit}>小时</Text>
            <Text style={$styles.durationValue}>{sleepData.todaySleepTime % 60}</Text>
            <Text style={$styles.durationUnit}>分钟</Text>
          </View>
          <Text style={$styles.targetText}>/ {Math.floor(sleepData.targetSleepTime / 60)}小时</Text>
        </View>

        {/* Right Module */}
        <View style={$styles.rightModule}>
          <View style={$styles.titleContainer}>
            <Text style={$styles.moduleTitle2}>睡眠质量</Text>
            <InfoIcon
              size={aw(14)}
              style={$styles.infoIcon}
              content="睡眠质量的综合评估，结合了睡眠效率和睡眠评分。"
            />
          </View>
          <Text style={$styles.qualityText}>
            {getSleepQualityLevel(sleepData.sleepEfficiency, sleepData.sleepScore || sleepData.sleepEfficiency)}
          </Text>
          <Text style={$styles.qualityPercentage}>{sleepData.sleepEfficiency}%</Text>
        </View>
      </View>
    </>
  )
}