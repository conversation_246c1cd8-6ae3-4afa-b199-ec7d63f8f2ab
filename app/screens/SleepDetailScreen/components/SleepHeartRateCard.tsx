import { useMemo, useEffect, useRef } from "react"
import { View, Dimensions, ViewStyle } from "react-native"
import { aw, ah } from "@/utils/adaptiveSize"
import { Text, StatusIndicator } from "@/components"
import { InfoIcon } from "@/components/InfoIcon"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"

const screenWidth = Dimensions.get("window").width
const CHART_HEIGHT = ah(150)
const CHART_WIDTH = screenWidth - aw(64)

interface SleepHeartRateCardProps {
  sleepHeartRateData: { time: string; value: number }[]
}

export const SleepHeartRateCard = ({ sleepHeartRateData }: SleepHeartRateCardProps) => {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)

  const chartRef = useRef<any>(null)

  // 处理心率数据
  const heartRateData = useMemo(() => {
    return sleepHeartRateData.map(item => ({ time: new Date(item.time).getTime(), value: item.value }))
  }, [sleepHeartRateData])

  // 计算心率统计数据
  const heartRateStats = useMemo(() => {
    if (sleepHeartRateData.length === 0) {
      return { average: 0, max: 0, min: 0, maxTime: '', minTime: '' }
    }

    const values = sleepHeartRateData.map(item => item.value)
    const average = Math.round(values.reduce((sum, value) => sum + value, 0) / values.length)
    
    let maxValue = values[0]
    let minValue = values[0]
    let maxIndex = 0
    let minIndex = 0
    
    values.forEach((value, index) => {
      if (value > maxValue) {
        maxValue = value
        maxIndex = index
      }
      if (value < minValue) {
        minValue = value
        minIndex = index
      }
    })
    
    const maxTime = new Date(sleepHeartRateData[maxIndex].time).toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
    const minTime = new Date(sleepHeartRateData[minIndex].time).toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
    
    return {
      average,
      max: maxValue,
      min: minValue,
      maxTime,
      minTime
    }
  }, [sleepHeartRateData])

  // ECharts 配置
  const chartOption = useMemo(() => {
    if (heartRateData.length === 0) {
      return {}
    }
    return {
      grid: {
        left: aw(16),
        right: aw(26),
        top: ah(10),
        bottom: ah(16),
      },
      xAxis: {
        type: 'time',
        show: false,
      },
      yAxis: {
        type: 'value',
        min: 40,
        position: 'right',
        axisLabel: {
          color: '#BCBFD0',
          interval: 0,
          showMaxLabel: true,
          showMinLabel: true,
        },
        splitNumber: 4,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: '#E5E5E5'
          }
        }
      },
      series: [{
        name: '心率',
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#F76B8A',
          width: aw(2),
        },
        data: heartRateData.map(item => [item.time, item.value])
      }]
    }
  }, [heartRateData])

  // 图表初始化
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      chartInstance = echarts.init(chartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: CHART_HEIGHT,
      })
      chartInstance.setOption(chartOption)
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [chartOption])

  const $styles = {
    container: {
      ...globalStyles.card,
      marginBottom: ah(theme.theme.spacing.md),
    } as ViewStyle,

    summaryContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: ah(16)
    } as ViewStyle,

    chartContainer: {
      height: CHART_HEIGHT,
      alignItems: "center",
    } as ViewStyle,

    timeContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: aw(16),
    } as ViewStyle,

    timeText: {
      fontSize: aw(12),
      color: '#BCBFD0',
    } as ViewStyle,

    metricsContainer: {
      flexDirection: "row",
      justifyContent: "flex-start",
      paddingTop: ah(16),
      paddingHorizontal: aw(16),
    } as ViewStyle,

    metricItem: {
      flex: 1,
    } as ViewStyle,

    metricLabel: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
      lineHeight: ah(14),
      fontFamily: theme.theme.typography.primary.bold,
      marginBottom: ah(8),
    } as ViewStyle,

    metricValueContainer: {
      flexDirection: "row",
      alignItems: "baseline",
      marginBottom: ah(8),
    } as ViewStyle,

    metricValue: {
      fontSize: aw(24),
      lineHeight: aw(26),
      fontWeight: "600",
      color: theme.theme.colors.text,
    } as ViewStyle,

    metricUnit: {
      fontSize: aw(12),
      color: '#BCBFD0',
      marginLeft: aw(4),
    } as ViewStyle,

    statusContainer: {
      flexDirection: "row",
      alignItems: "center",
    } as ViewStyle,

    checkIcon: {
      width: aw(12),
      height: aw(12),
      borderRadius: aw(6),
      backgroundColor: '#4CAF50',
      marginRight: aw(4),
    } as ViewStyle,

    statusText: {
      fontSize: aw(12),
      color: '#4CAF50',
    } as ViewStyle,

    metricTime: {
      fontSize: aw(12),
      color: theme.theme.colors.palette.primary200,
    } as ViewStyle,
  }

  return (
    <View style={$styles.container}>
      <View style={$styles.summaryContainer}>
        <Text style={[themedStyles.sectionTitle, { marginRight: aw(4) }]} preset="subheading" text="睡眠时心率" />
        <InfoIcon icon="info" size={aw(16)} color={'#BCBFD0'} title={'睡眠时心率'} content={'暂无指标说明'} />
      </View>

      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>
      <View style={$styles.timeContainer}>
        <Text style={$styles.timeText} text={sleepHeartRateData[0]?.time ? new Date(sleepHeartRateData[0].time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false }) : ''} />
        <Text style={$styles.timeText} text={sleepHeartRateData[Math.floor(sleepHeartRateData.length / 2)]?.time ? new Date(sleepHeartRateData[Math.floor(sleepHeartRateData.length / 2)].time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false }) : ''} />
        <Text style={$styles.timeText} text={sleepHeartRateData[sleepHeartRateData.length - 1]?.time ? new Date(sleepHeartRateData[sleepHeartRateData.length - 1].time).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false }) : ''} />
      </View>

      {/* 睡眠指标 */}
      <View style={$styles.metricsContainer}>
        <View style={$styles.metricItem}>
          <Text style={$styles.metricLabel} text="平均" />
          <View style={$styles.metricValueContainer}>
            <Text style={$styles.metricValue} text={heartRateStats.average.toString()} />
            <Text style={$styles.metricUnit} text="bpm" />
          </View>
          <StatusIndicator status="normal" />
        </View>

        <View style={$styles.metricItem}>
          <Text style={$styles.metricLabel} text="最高" />
          <View style={$styles.metricValueContainer}>
            <Text style={$styles.metricValue} text={heartRateStats.max.toString()} />
            <Text style={$styles.metricUnit} text="bpm" />
          </View>
          <Text style={$styles.metricTime} text={heartRateStats.maxTime} />
        </View>

        <View style={$styles.metricItem}>
          <Text style={$styles.metricLabel} text="最低" />
          <View style={$styles.metricValueContainer}>
            <Text style={$styles.metricValue} text={heartRateStats.min.toString()} />
            <Text style={$styles.metricUnit} text="bpm" />
          </View>
          <Text style={$styles.metricTime} text={heartRateStats.minTime} />
        </View>
      </View>

    </View>
  )
}