import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { ah, aw, af } from "@/utils/adaptiveSize"
import { Text, StatusIndicator } from "@/components"
import { InfoIcon } from "@/components/InfoIcon"
import { useAppTheme } from "@/utils/useAppTheme"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"

interface SleepVitalSignsData {
  averageHeartRate: number
  averageRespiratoryRate: number
  averageBloodOxygenSaturation: number
  averageWristTemperature: number
}

interface SleepVitalSignsCardProps {
  data: SleepVitalSignsData
}

export const SleepVitalSignsCard: FC<SleepVitalSignsCardProps> = ({ data }) => {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)

  const $styles = {
    container: {
      ...globalStyles.card,
      marginBottom: ah(theme.theme.spacing.md),
    } as ViewStyle,

    summaryContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: ah(16)
    } as ViewStyle,

    metricsGrid: {
      gap: ah(12),
    } as ViewStyle,

    metricsRow: {
      flexDirection: "row",
      gap: aw(16),
    } as ViewStyle,

    metricCard: {
      flex: 1,
      backgroundColor: "#F7F7F7",
      borderRadius: ah(16),
      padding: ah(16),
    } as ViewStyle,

    metricTitle: {
      fontSize: af(12),
      color: theme.theme.colors.palette.primary200,
      marginBottom: ah(16),
    } as TextStyle,

    valueContainer: {
      flexDirection: "row",
      alignItems: "baseline",
      marginBottom: ah(8),
    } as ViewStyle,

    metricValue: {
      fontSize: af(24),
      lineHeight: aw(26),
      fontWeight: "600",
      color: theme.theme.colors.palette.primary600,
    } as TextStyle,

    metricUnit: {
      fontSize: af(14),
      color: theme.theme.colors.textDim,
      marginLeft: aw(4),
    } as TextStyle,
  }

  return (
    <View style={$styles.container}>
      {/* 标题区域 */}
      <View style={$styles.summaryContainer}>
        <Text
          style={[themedStyles.sectionTitle, { marginRight: aw(4) }]}
          text="睡眠生命体征"
        />
        <InfoIcon content="睡眠生命体征相关信息" />
      </View>

      {/* 生命体征指标 */}
      <View style={$styles.metricsGrid}>
        <View style={$styles.metricsRow}>
          {/* 平均心率 */}
          <View style={$styles.metricCard}>
            <Text style={$styles.metricTitle} text="平均心率" />
            <View style={$styles.valueContainer}>
              <Text style={$styles.metricValue} text={data.averageHeartRate.toString()} />
              <Text style={$styles.metricUnit} text="bpm" />
            </View>
            <StatusIndicator status="normal" />
          </View>

          {/* 平均手腕温度 */}
          <View style={$styles.metricCard}>
            <Text style={$styles.metricTitle} text="平均手腕温度" />
            <View style={$styles.valueContainer}>
              <Text style={$styles.metricValue} text={data.averageWristTemperature.toString()} />
              <Text style={$styles.metricUnit} text="℃" />
            </View>
            <StatusIndicator status="error" />
          </View>
        </View>

        <View style={$styles.metricsRow}>
          {/* 平均呼吸频率 */}
          <View style={$styles.metricCard}>
            <Text style={$styles.metricTitle} text="平均呼吸频率" />
            <View style={$styles.valueContainer}>
              <Text style={$styles.metricValue} text={data.averageRespiratoryRate.toString()} />
              <Text style={$styles.metricUnit} text="br/min" />
            </View>
            <StatusIndicator status="normal" />
          </View>

          {/* 平均血氧饱和度 */}
          <View style={$styles.metricCard}>
            <Text style={$styles.metricTitle} text="平均血氧饱和度" />
            <View style={$styles.valueContainer}>
              <Text style={$styles.metricValue} text={data.averageBloodOxygenSaturation.toString()} />
              <Text style={$styles.metricUnit} text="%" />
            </View>
            <StatusIndicator status="error" />
          </View>
        </View>
      </View>
    </View>
  )
}