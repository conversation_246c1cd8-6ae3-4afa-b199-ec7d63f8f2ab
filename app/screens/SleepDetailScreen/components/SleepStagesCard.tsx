import { FC, useRef, useMemo, useEffect } from "react"
import { View, ViewStyle, TextStyle, Dimensions } from "react-native"
import { observer } from "mobx-react-lite"
import { Text, InfoIcon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, ah, af } from "@/utils/adaptiveSize"
import { $styles as globalStyles, createThemedStyles } from "@/theme/styles"
import { typography } from "@/theme/typography"
import { SleepStage } from "@/data/types"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"

interface SleepStageSummary {
  name: string
  value: string
  percentage: number
}

interface SleepStagesCardProps {
  todaySleepSummary: SleepStageSummary[]
  sleepStagesData: SleepStage[]
}

const screenWidth = Dimensions.get("window").width
const CHART_HEIGHT = 150
const CHART_WIDTH = screenWidth - aw(64)

export const SleepStagesCard: FC<SleepStagesCardProps> = observer(function SleepStagesCard({
  todaySleepSummary,
  sleepStagesData,
}) {
  const theme = useAppTheme()
  const themedStyles = createThemedStyles(theme)
  const chartRef = useRef<any>(null)

  // 睡眠阶段数据
  const sleepStages = useMemo(() => {
    if (!todaySleepSummary) {
      return []
    }
    return todaySleepSummary.map((stage, index) => {
      const colors = ["#AB81FF", "#D8C8FE", "#F1E9FE", "#FDEFA6"]
      return {
        name: stage.name,
        value: stage.value,
        percentage: stage.percentage,
        color: colors[index] || "#AB81FF",
      }
    })
  }, [todaySleepSummary])

  // 睡眠时间轴数据
  const sleepTimelineData = useMemo(() => {
    if (!sleepStagesData) {
      return []
    }
    const data: Array<{
      name: string
      value: [number, number, number, string]
      itemStyle: { color: string }
    }> = []

    const yAxisData = ['深睡', '浅睡', 'REM', '清醒']
    const stageColorMapping: { [key: string]: string } = {
      深睡: "#AB81FF",
      浅睡: "#D8C8FE",
      REM: "#F1E9FE",
      清醒: "#FDEFA6",
    }

    sleepStagesData.forEach((stageInfo) => {
      const categoryIndex = yAxisData.indexOf(stageInfo.stage)
      const startTime = new Date(stageInfo.startTime).getTime()
      const endTime = new Date(stageInfo.endTime).getTime()

      if (categoryIndex !== -1) {
        data.push({
          name: stageInfo.stage,
          value: [categoryIndex, startTime, endTime, stageInfo.stage],
          itemStyle: { color: stageColorMapping[stageInfo.stage] },
        })
      }
    })

    // 按开始时间对数据进行排序
    data.sort((a, b) => a.value[1] - b.value[1])

    return data
  }, [sleepStagesData])

  // ECharts 配置
  const chartOption = useMemo(() => {

    if (sleepTimelineData.length === 0) {
      return {}
    }

    const minTime = new Date(sleepTimelineData[0].value[1])
    minTime.setMinutes(minTime.getMinutes() - 15)

    const maxTime = new Date(sleepTimelineData[sleepTimelineData.length - 1].value[2])
    maxTime.setMinutes(maxTime.getMinutes() + 15)

    return {
      tooltip: {
        formatter: (params: any) => {
          const data = params.data
          const startTime = new Date(data.value[1])
          const endTime = new Date(data.value[2])
          return `${data.value[3]} ${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}~${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`
        }
      },
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        // height: '60%'
      },
      xAxis: {
        type: 'time',
        min: minTime.getTime(),
        max: maxTime.getTime(),
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'category',
        data: ['深睡', '浅睡', 'REM', '清醒'],
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false }
      },
      series: [{
        type: 'custom',
        renderItem: (params: any, api: any) => {
          const categoryIndex = api.value(0)
          const start = api.coord([api.value(1), categoryIndex])
          const end = api.coord([api.value(2), categoryIndex])
          const height = api.size([0, 1])[1]
          const color = api.visual('color')
          return {
            type: 'rect',
            shape: {
              x: start[0],
              y: start[1] - height / 2,
              width: end[0] - start[0],
              height: height,
            },
            style: {
              fill: color,
              stroke: 'transparent',
              lineWidth: 0
            }
          }
        },
        data: sleepTimelineData
      }]
    }
  }, [sleepTimelineData])

  // 图表初始化
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      chartInstance = echarts.init(chartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: CHART_HEIGHT,
      })
      chartInstance.setOption(chartOption)
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [chartOption])

  // 早期返回条件 - 在所有Hooks调用完成后
  if (!todaySleepSummary || !sleepStagesData) {
    return null
  }

  const $styles = {
    container: {
      ...globalStyles.card,
      marginBottom: ah(theme.theme.spacing.md),
    } as ViewStyle,

    summaryContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: ah(16)
    } as ViewStyle,

    timeContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: ah(theme.theme.spacing.md),
    } as ViewStyle,
    timeText: {
      flexDirection: "row",
      alignItems: "center",
    } as ViewStyle,

    timeLabel: {
      fontSize: af(12),
      color: '#BCBFD0',
      lineHeight: ah(16),
      marginLeft: aw(2),
    } as TextStyle,

    timeValue: {
      fontSize: af(12),
      color: '#BCBFD0',
      lineHeight: ah(16),
    } as TextStyle,

    stagesContainer: {
      // marginTop: ah(16),
      paddingHorizontal: aw(16),
      paddingVertical: ah(10),
      backgroundColor: '#F7F7F7',
      borderRadius: aw(12),
    } as ViewStyle,

    stageRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: ah(6),
    } as ViewStyle,

    lastStageRow: {
      borderBottomWidth: 0,
    } as ViewStyle,

    stageLeft: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    } as ViewStyle,

    stageColor: {
      width: aw(8),
      height: ah(8),
      borderRadius: aw(8),
      marginRight: aw(4),
    } as ViewStyle,

    stageName: {
      fontSize: af(12),
      lineHeight: ah(16),
      marginRight: aw(4),
      color: theme.theme.colors.text,
      fontFamily: typography.primary.medium,
    } as TextStyle,

    stageValue: {
      fontSize: af(12),
      fontFamily: typography.primary.bold,
      color: '#8489B4',
      lineHeight: af(16),
    } as TextStyle,

    stageUnit: {
      fontSize: af(12),
      color: theme.theme.colors.palette.primary200,
      marginLeft: aw(4),
    } as TextStyle,

    stageValueContainer: {
      flexDirection: "row",
      alignItems: "baseline",
    } as ViewStyle,

    chartContainer: {
      height: CHART_HEIGHT,
      marginBottom: ah(8),
      alignItems: "center",
    } as ViewStyle,

    legendContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      marginTop: ah(8),
      marginBottom: ah(30),
    } as ViewStyle,

    legendItem: {
      flexDirection: "row",
      alignItems: "center",
    } as ViewStyle,

    legendColor: {
      width: aw(3),
      height: ah(10),
      marginRight: aw(4),
    } as ViewStyle,

    legendColorDeepSleep: {
      width: aw(3),
      height: ah(10),
      marginRight: aw(4),
      backgroundColor: '#AB81FF',
    } as ViewStyle,

    legendColorLightSleep: {
      width: aw(3),
      height: ah(10),
      marginRight: aw(4),
      backgroundColor: '#D8C8FE',
    } as ViewStyle,

    legendColorREM: {
      width: aw(3),
      height: ah(10),
      marginRight: aw(4),
      backgroundColor: '#F1E9FE',
    } as ViewStyle,

    legendColorAwake: {
      width: aw(3),
      height: ah(10),
      marginRight: aw(4),
      backgroundColor: '#FDEFA6',
    } as ViewStyle,

    legendText: {
      fontSize: af(12),
      lineHeight: ah(16),
      color: theme.theme.colors.palette.primary200,
    } as TextStyle,
  }
  return (
    <View style={$styles.container}>
      <View style={$styles.summaryContainer}>
        <Text style={[themedStyles.sectionTitle, { marginRight: aw(4) }]} preset="subheading" text="睡眠阶段" />
        <InfoIcon icon="info" size={aw(16)} color={'#BCBFD0'} title={'睡眠阶段'} content={'暂无指标说明'} />
      </View>

      {/* 睡眠阶段图例 */}
      <View style={$styles.legendContainer}>
        <View style={$styles.legendItem}>
          <View style={$styles.legendColorDeepSleep} />
          <Text style={$styles.legendText} text="深睡" />
        </View>
        <View style={$styles.legendItem}>
          <View style={$styles.legendColorLightSleep} />
          <Text style={$styles.legendText} text="浅睡" />
        </View>
        <View style={$styles.legendItem}>
          <View style={$styles.legendColorREM} />
          <Text style={$styles.legendText} text="快速眼动" />
        </View>
        <View style={$styles.legendItem}>
          <View style={$styles.legendColorAwake} />
          <Text style={$styles.legendText} text="清醒" />
        </View>
      </View>

      {/* 睡眠图 */}
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>

      {/* 睡眠时间 */}
      <View style={$styles.timeContainer}>
        <Text style={$styles.timeText}>
          <Text style={$styles.timeValue} text={`${new Date(sleepStagesData[0].startTime).getHours().toString().padStart(2, '0')}:${new Date(sleepStagesData[0].startTime).getMinutes().toString().padStart(2, '0')}`} />
          <Text style={$styles.timeLabel} text="入睡" />
        </Text>
        <Text style={$styles.timeText}>
          <Text style={$styles.timeValue} text={`${new Date(sleepStagesData[sleepStagesData.length - 1].endTime).getHours().toString().padStart(2, '0')}:${new Date(sleepStagesData[sleepStagesData.length - 1].endTime).getMinutes().toString().padStart(2, '0')}`} />
          <Text style={$styles.timeLabel} text="醒来" />
        </Text>
      </View>


      {/* 睡眠阶段详情 */}
      <View style={$styles.stagesContainer}>
        {sleepStages.map((stage, index) => (
          <View
            key={stage.name}
            style={[
              $styles.stageRow,
              index === sleepStages.length - 1 && $styles.lastStageRow,
            ]}
          >
            <View style={$styles.stageLeft}>
              <View style={[$styles.stageColor, { backgroundColor: stage.color }]} />
              <Text style={$styles.stageName} text={stage.name} />
              <Text style={$styles.stageValue} text={stage.value} />
            </View>
            <View style={$styles.stageValueContainer}>
              <Text style={$styles.stageUnit} text={`${stage.percentage}%`} />
            </View>
          </View>
        ))}
      </View>
    </View>
  )
})