import { FC, useRef, useState, useEffect } from "react"
import { View, ViewStyle, ScrollView, Dimensions, NativeSyntheticEvent, NativeScrollEvent } from "react-native"
import { observer } from "mobx-react-lite"
import { Screen, Header } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, ah, af } from "@/utils/adaptiveSize"
import { MetricsCarouselCard } from "./components/MetricsCarouselCard"
import { useRoute, RouteProp } from "@react-navigation/native"
import { useMemo } from "react"
import { AppStackParamList } from "@/navigators/AppNavigator"
import { useStores } from "@/models/helpers/useStores"

const { width: screenWidth } = Dimensions.get('window')

interface MetricsDetailScreenProps extends AppStackScreenProps<"MetricsDetailScreen"> { }
export const MetricsDetailScreen: FC<MetricsDetailScreenProps> = observer(function MetricsDetailScreen(_props) {
  const theme = useAppTheme()
  const $styles = useMemo(() => createStyles(theme), [theme])
  const scrollViewRef = useRef<ScrollView>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const route = useRoute<RouteProp<AppStackParamList, 'MetricsDetailScreen'>>()
  const { healthStore } = useStores()
  const { metricId } = route.params

  // 处理滚动事件
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x
    const cardWidth = screenWidth
    const index = Math.round(contentOffsetX / cardWidth)
    setCurrentIndex(index)
  }
  // 获取store
  const metricsStoreData = healthStore.metricsData
  useEffect(() => {
    if (!metricsStoreData.length) return

    const index = metricsStoreData.findIndex((item) => item.id === metricId)
    console.log("🚀 ~ useEffect ~ index:", index)
    setCurrentIndex(index)


    // 直接跳转到对应位置（无动画）
    if (scrollViewRef.current && index >= 0) {
      setTimeout(() => {
        scrollViewRef.current?.scrollTo({
          x: screenWidth * index,
          animated: false  // 禁用动画
        })
      }, 0)
    }
  }, [metricId, metricsStoreData])




  // 渲染指示器
  const renderIndicators = () => (
    <View style={$styles.indicatorContainer}>
      {metricsStoreData.map((_, index) => (
        <View
          key={index}
          style={[
            $styles.indicator,
            index === currentIndex && $styles.activeIndicator
          ]}
        />
      ))}
    </View>
  )

  return (
    <>
      <Header
        title="核心指标"
        leftIcon="caretLeft"
        onLeftPress={() => _props.navigation.goBack()}
        leftIconColor={theme.theme.colors.palette.primary500}
        titleStyle={{ color: theme.theme.colors.palette.primary500 }}
      />
      <Screen
        preset="fixed"
        contentContainerStyle={$styles.screenContentContainer}
        safeAreaEdges={["bottom"]}
      >

        <View style={$styles.container}>
          {/* 轮播卡片 */}
          <View style={$styles.carouselContainer}>
            <ScrollView
              ref={scrollViewRef}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onScroll={handleScroll}
              scrollEventThrottle={16}
              style={$styles.scrollView}
              contentContainerStyle={$styles.scrollContent}
            >

              {metricsStoreData.map((item) => (
                <MetricsCarouselCard
                  key={item.id}
                  currentValue={item?.currentValue}
                  eChartData={item.eChartData}
                  icon={item.icon}
                  title={item.title}
                  unit={item.unit}
                  eChartLineColor={item.eChartLineColor}
                  normalRange={item.normalRange}
                  avgLast7Days={item.avgLast7Days}
                  changeFromLast7Days={item.changeFromLast7Days}
                  metricDefinition={item.metricDefinition}
                  metricTitle={item.title}
                />
              ))}
            </ScrollView>

            {/* 指示器 */}
            {renderIndicators()}
          </View>
        </View>
      </Screen>
    </>

  )
})

const createStyles = (theme: ReturnType<typeof useAppTheme>) => {
  return {
    screenContentContainer: {
      paddingBottom: theme.theme.spacing.md,
    } as ViewStyle,

    container: {
      flex: 1,
    } as ViewStyle,

    header: {
      paddingHorizontal: aw(theme.theme.spacing.md),
      marginBottom: ah(20),
    } as ViewStyle,

    title: {
      fontSize: af(24),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.text,
      marginBottom: ah(8),
    } as ViewStyle,

    subtitle: {
      fontSize: af(16),
      color: theme.theme.colors.palette.primary200,
      fontFamily: theme.theme.typography.primary.medium,
    } as ViewStyle,

    carouselContainer: {
      flex: 1,
    } as ViewStyle,

    scrollView: {
      flex: 1,
      minHeight: ah(580),
    } as ViewStyle,

    scrollContent: {
      paddingHorizontal: 0,
      alignItems: 'flex-start',
    } as ViewStyle,

    indicatorContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: ah(20),
      paddingHorizontal: aw(theme.theme.spacing.md),
    } as ViewStyle,

    indicator: {
      width: aw(16),
      height: ah(4),
      borderRadius: aw(2),
      marginHorizontal: aw(4),
      backgroundColor: '#DBDDE5',
    } as ViewStyle,

    activeIndicator: {
      width: aw(16),
      height: ah(4),
      borderRadius: aw(2),
      backgroundColor: theme.theme.colors.palette.primary100,
      elevation: 2,
    } as ViewStyle,
  }
}