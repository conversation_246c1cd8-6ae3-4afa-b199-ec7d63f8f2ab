import { FC, useRef, useMemo, useEffect } from "react"
import { View, ViewStyle, TextStyle, Dimensions } from "react-native"
import { Text, Icon, IconTypes, InfoIcon, StatusIndicator } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, ah, af } from "@/utils/adaptiveSize"
import { $styles as globalStyles } from "@/theme/styles"
import echarts from "../../../utils/echarts"
import SvgChart from "@wuba/react-native-echarts/svgChart"
import type { ECharts } from "echarts/core"
import { MarkAreaComponent } from 'echarts/components'

const { width: screenWidth } = Dimensions.get('window')

interface MetricsCarouselCardProps {
  currentValue: number
  eChartData: { time: string; value: number }[]
  title: string
  unit: string
  icon: IconTypes
  eChartLineColor: string
  normalRange: [number, number]
  avgLast7Days: number
  changeFromLast7Days: number
  /** 指标定义说明文字 */
  metricDefinition?: string
  /** 指标说明标题 */
  metricTitle?: string
}
// _avgLast7Days, _changeFromLast7Days
export const MetricsCarouselCard: FC<MetricsCarouselCardProps> = ({
  currentValue, eChartData, title, unit, icon, eChartLineColor, normalRange, avgLast7Days, changeFromLast7Days, metricDefinition, metricTitle
}) => {
  const theme = useAppTheme()
  const chartRef = useRef<any>(null)

  // 判断当前值是否在正常范围内
  const isInNormalRange = currentValue >= normalRange[0] && currentValue <= normalRange[1]

  const CHART_WIDTH = screenWidth - aw(64)
  const CHART_HEIGHT = ah(260)

  // 图表配置
  const chartOption = useMemo(() => {
    // 为每种状态创建动态的rich样式
    const richStyles: any = {
      value: {
        fontSize: af(12),
        color: '#333',
        fontWeight: '600'
      },
      time: {
        fontSize: af(11),
        color: '#666',
        lineHeight: af(18)
      }
    }

    return {

      tooltip: {
        trigger: 'item',
        triggerOn: 'click',
        confine: true,
        backgroundColor: '#FFFFFF',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          fontSize: af(12),
          lineHeight: af(16),
        },
        padding: [ah(6), aw(10)],
        extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 9999;',
        formatter: (params: any) => {
          const dataIndex = params.dataIndex
          const dataItem = eChartData[dataIndex]
          return `{ {value | ${dataItem.value}\n{time|· ${dataItem.time}}`
        },
        rich: richStyles
      },
      grid: {
        left: aw(10),
        right: aw(30),
        top: ah(10),
        bottom: ah(30),
        containLabel: false,
        backgroundColor: '#FFFFFF',
      },
      xAxis: {
        type: 'category',
        data: eChartData.map(item => item.time),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#BCBFD0',
          fontSize: aw(12),
          margin: ah(12),
        },
      },
      yAxis: {
        type: 'value',
        show: true,
        position: 'right',
        min: (() => {
          const values = eChartData.map(item => item.value);
          return Math.max(0, Math.min(...values)); // 取最小值的90%，且不小于0
        })(),
        max: (() => {
          const values = eChartData.map(item => item.value);
          return Math.max(...values); // 取最大值的110%
        })(),
        axisLabel: {
          color: '#BCBFD0',
          fontSize: aw(12),
          margin: aw(6),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E5E5E5',
            type: 'dashed',
          },
        },
      },

      series: [
        {
          type: 'line',
          data: eChartData.map(item => item.value),
          itemStyle: {
            color: '#FFFFFF',
            borderColor: eChartLineColor,
            borderWidth: aw(2),
          },
          symbol: 'circle',
          symbolSize: aw(12),
          lineStyle: {
            color: eChartLineColor,
            width: aw(1),
            type: 'dashed',
          },
          smooth: false,
          showSymbol: true,
          markArea: {
            silent: true,
            itemStyle: {
              color: 'rgba(245, 246, 255, 0.5)',
              borderColor: 'transparent',
            },
            data: [
              [
                {
                  yAxis: normalRange[0],
                },
                {
                  yAxis: normalRange[1],
                }
              ]
            ]
          }
        },
      ],
    }
  }, [eChartData, theme])

  // 初始化图表
  useEffect(() => {
    let chartInstance: ECharts | undefined
    if (chartRef.current && Object.keys(chartOption).length > 0) {
      // 注册 MarkAreaComponent
      echarts.use([MarkAreaComponent]);

      chartInstance = echarts.init(chartRef.current, "light", {
        renderer: "svg",
        width: CHART_WIDTH,
        height: CHART_HEIGHT,
      })
      chartInstance.setOption(chartOption)

      // 添加移动端触摸事件处理
      chartInstance.on('click', (params: any) => {
        chartInstance?.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        })
      })
    }
    return () => {
      chartInstance?.dispose()
    }
  }, [chartOption, CHART_WIDTH, CHART_HEIGHT])

  const $styles = {
    card: {
      ...globalStyles.card,
      width: screenWidth - aw(32),
      marginHorizontal: aw(16),
      // 确保iOS上的布局
      minHeight: ah(400),
      alignSelf: 'center',
    } as ViewStyle,

    // 头部模块 - 垂直布局
    headerModule: {
      marginBottom: ah(24),
    } as ViewStyle,

    // 标题模块 - 水平布局
    titleModule: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: ah(16),
    } as ViewStyle,

    titleLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    titleText: {
      fontSize: af(14),
      lineHeight: ah(20),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary500,
      marginHorizontal: aw(4),
    } as TextStyle,

    // 数值状态模块
    valueStatusModule: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: ah(16),
    } as ViewStyle,

    valueLeft: {
      flexDirection: 'row',
      alignItems: 'flex-end',
    } as ViewStyle,

    mainValue: {
      fontSize: af(36),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary500,
      lineHeight: af(40),
    } as TextStyle,

    unitText: {
      fontSize: af(12),
      lineHeight: af(16),
      color: theme.theme.colors.palette.primary100,
      fontFamily: theme.theme.typography.primary.medium,
      marginLeft: aw(4),
      marginBottom: ah(8),
    } as TextStyle,

    statusRight: {
      alignItems: 'flex-end',
    } as ViewStyle,

    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: aw(9),
      paddingVertical: ah(4),
      borderRadius: aw(8),
    } as ViewStyle,

    statusBadgeNormal: {
      backgroundColor: '#E3F2FD',
    } as ViewStyle,

    statusBadgeError: {
      backgroundColor: '#FFEBEE',
    } as ViewStyle,

    statusText: {
      fontSize: af(12),
      lineHeight: ah(16),
      fontFamily: theme.theme.typography.primary.medium,
      marginLeft: aw(4),
    } as TextStyle,

    statusTextNormal: {
      color: '#6892D5',
    } as TextStyle,

    statusTextError: {
      color: '#E84A5F',
    } as TextStyle,
    color: '#FFFFFF',
    marginLeft: aw(4),


    // 说明文字模块
    descriptionModule: {
      backgroundColor: theme.theme.colors.palette.theme100,
      borderRadius: aw(24),
      paddingHorizontal: aw(16),
      paddingVertical: ah(8),
    } as ViewStyle,

    descriptionText: {
      fontSize: af(14),
      color: theme.theme.colors.palette.primary200,
      textAlign: 'center',
      lineHeight: ah(16),
    } as TextStyle,

    descriptionTextHighlight: {
      fontSize: af(14),
      color: '#6892D5',
      textAlign: 'center',
      lineHeight: ah(16),
    } as TextStyle,

    chartContainer: {
      width: '100%',
      alignItems: 'center',
    } as ViewStyle,

    legendContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginTop: aw(theme.theme.spacing.sm),
      paddingHorizontal: aw(theme.theme.spacing.xs),
    } as ViewStyle,

    legendItem: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    legendDot: {
      width: aw(8),
      height: aw(8),
      borderRadius: aw(4),
      marginRight: aw(6),
    } as ViewStyle,

    legendText: {
      fontSize: af(12),
      color: theme.theme.colors.palette.primary200,
      lineHeight: af(16),
      fontFamily: theme.theme.typography.primary.medium,
    } as TextStyle,

    // 指标卡片样式
    metricsCardsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: ah(16),
      // paddingHorizontal: aw(4),
    } as ViewStyle,

    metricsCard: {
      flex: 1,
      backgroundColor: '#F7F7F7',
      borderRadius: aw(12),
      paddingHorizontal: aw(16),
      paddingVertical: ah(16),
      marginHorizontal: aw(7),
      minHeight: ah(100),
    } as ViewStyle,

    cardTitle: {
      fontSize: af(14),
      lineHeight: ah(16),
      color: theme.theme.colors.palette.primary200,
      fontFamily: theme.theme.typography.primary.medium,
      marginBottom: ah(16),
    } as TextStyle,

    cardValueContainer: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      marginBottom: ah(12),
    } as ViewStyle,

    cardMainValue: {
      fontSize: af(24),
      fontFamily: theme.theme.typography.primary.bold,
      color: theme.theme.colors.palette.primary500,
      lineHeight: af(28),
    } as TextStyle,

    cardUnitText: {
      fontSize: af(12),
      color: theme.theme.colors.palette.primary100,
      fontFamily: theme.theme.typography.primary.medium,
      marginLeft: aw(2),
      marginBottom: ah(4),
    } as TextStyle,

    cardStatusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    cardStatusText: {
      fontSize: af(12),
      color: '#6892D5',
      fontFamily: theme.theme.typography.primary.medium,
      marginLeft: aw(4),
    } as TextStyle,

    cardBottomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    } as ViewStyle,

    cardBottomText: {
      fontSize: af(12),
      color: theme.theme.colors.palette.primary200,
      fontFamily: theme.theme.typography.primary.medium,
      marginRight: aw(2),
    } as TextStyle,
  }

  return (
    <View style={$styles.card}>
      {/* 头部模块 - 垂直布局 */}
      <View style={$styles.headerModule}>
        {/* 标题模块 - 水平布局 */}
        <View style={$styles.titleModule}>
          <View style={$styles.titleLeft}>
            <Icon icon={icon} size={aw(16)} useOriginalColor />
            <Text style={$styles.titleText}>{title}</Text>
            <InfoIcon
              icon="info"
              size={aw(16)}
              color={'#BCBFD0'}
              title={metricTitle || '指标说明'}
              content={metricDefinition || '暂无指标说明'}
            />
          </View>
        </View>

        {/* 数值状态模块 */}
        <View style={$styles.valueStatusModule}>
          <View style={$styles.valueLeft}>
            <Text style={$styles.mainValue}>{currentValue}</Text>
            <Text style={$styles.unitText}>{unit}</Text>
          </View>
          <View style={$styles.statusRight}>
            <View style={[
              $styles.statusBadge,
              isInNormalRange ? $styles.statusBadgeNormal : $styles.statusBadgeError
            ]}>
              <Icon
                icon={isInNormalRange ? "indicatorNormal" : "indicatorError"}
                size={aw(12)}
                useOriginalColor
              />
              <Text style={[
                $styles.statusText,
                isInNormalRange ? $styles.statusTextNormal : $styles.statusTextError
              ]}>{isInNormalRange ? "正常" : "异常"}</Text>
            </View>
          </View>
        </View>

        {/* 说明文字模块 */}
        <View style={$styles.descriptionModule}>
          <Text style={$styles.descriptionText}>
            你最新的数值{isInNormalRange ? "处在正常范围 " : "超出正常范围 "}
            <Text style={$styles.descriptionTextHighlight}>
              {normalRange[0]}~{normalRange[1]}{unit}
            </Text>
          </Text>
        </View>
      </View>

      {/* 趋势图 */}
      <View style={$styles.chartContainer}>
        <SvgChart ref={chartRef} />
      </View>

      {/* 指标卡片 */}
      <View style={$styles.metricsCardsContainer}>
        {/* 左侧卡片 - 过去7天平均值 */}
        <View style={$styles.metricsCard}>
          <Text style={$styles.cardTitle}>过去7天平均值</Text>
          <View style={$styles.cardValueContainer}>
            <Text style={$styles.cardMainValue}>{avgLast7Days}</Text>
            <Text style={$styles.cardUnitText}>/{unit}</Text>
          </View>
          <StatusIndicator status="normal" />
        </View>

        {/* 右侧卡片 - 变化趋势 */}
        <View style={$styles.metricsCard}>
          <Text style={$styles.cardTitle}>变化趋势</Text>
          <View style={$styles.cardValueContainer}>
            <Text style={$styles.cardMainValue}>{Math.abs(changeFromLast7Days)}</Text>
            <Text style={$styles.cardUnitText}>%</Text>
          </View>
          <View style={$styles.cardBottomContainer}>
            <Text style={$styles.cardBottomText}>较过去7天均值</Text>
            {changeFromLast7Days !== 0 && (
              <Icon
                icon={changeFromLast7Days > 0 ? "arrowUp" : "arrowDown"}
                size={aw(12)}
                useOriginalColor
              />
            )}
          </View>
        </View>
      </View>
    </View>
  )
}

