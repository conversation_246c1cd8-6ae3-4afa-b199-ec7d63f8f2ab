import { useCallback } from "react"
import { useStores } from "../models/helpers/useStores"
import AppleHealthKit, { HealthValue, HealthKitPermissions } from "react-native-health"

// 获取原始逐搏数据
type HeartbeatSample = {
  startDate: string // 开始时间
  endDate: string // 结束时间
  id?: string // 可选ID
  heartbeatSeries?: any // 元数据
}

// 不再需要 options 参数，因为现在由组件控制初始化

export const useHealthData = () => {
  const { healthStore } = useStores()
  // 不再需要 isInitializing 状态，由组件管理

  // 初始化 HealthKit 并请求权限
  const initializeHealthKit = useCallback(async (): Promise<{
    success: boolean
    error?: string
  }> => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      // 检查 HealthKit 是否可用
      const isAvailable = await new Promise<boolean>((resolve) => {
        AppleHealthKit.isAvailable((err, available) => {
          if (err) {
            console.error("检查 HealthKit 可用性时出错:", err)
            resolve(false)
            return
          }
          resolve(available)
        })
      })

      if (!isAvailable) {
        const errorMessage = "HealthKit 在此设备上不可用"
        console.error(errorMessage)
        healthStore.setError(errorMessage)
        healthStore.setPermissionsGranted(false)
        return { success: false, error: errorMessage }
      }

      // 请求权限
      const permissions: HealthKitPermissions = {
        permissions: {
          read: [
            AppleHealthKit.Constants.Permissions.HeartRate,
            AppleHealthKit.Constants.Permissions.StepCount,
            AppleHealthKit.Constants.Permissions.HeartRateVariability,
            AppleHealthKit.Constants.Permissions.RestingHeartRate,
            AppleHealthKit.Constants.Permissions.HeartbeatSeries, // 心跳序列数据
            AppleHealthKit.Constants.Permissions.DateOfBirth, // 出生日期
            AppleHealthKit.Constants.Permissions.BiologicalSex, // 生理性别
            AppleHealthKit.Constants.Permissions.Height, // 身高
            AppleHealthKit.Constants.Permissions.Weight, // 体重
            AppleHealthKit.Constants.Permissions.SleepAnalysis, // 睡眠分析
            AppleHealthKit.Constants.Permissions.RespiratoryRate, // 呼吸率
            AppleHealthKit.Constants.Permissions.OxygenSaturation, // 血氧饱和度
            AppleHealthKit.Constants.Permissions.BodyTemperature, // 手腕温度
          ],
          write: [],
        },
      }

      console.log("请求 HealthKit 权限...")

      // 使用 Promise 包装回调函数
      const granted = await new Promise<boolean>((resolve) => {
        AppleHealthKit.initHealthKit(permissions, (error: string) => {
          if (error) {
            console.error("HealthKit 初始化错误:", error)
            healthStore.setError(`HealthKit 初始化错误: ${error}`)
            healthStore.setPermissionsGranted(false)
            resolve(false)
            return
          }
          resolve(true)
        })
      })

      healthStore.setPermissionsGranted(granted)
      if (!granted) {
        const errorMessage = "未获得必要的 HealthKit 权限"
        console.error(errorMessage)
        healthStore.setError(errorMessage)
        return { success: false, error: errorMessage }
      }

      return { success: true }
    } catch (error) {
      const errorMessage = `HealthKit 初始化失败: ${error instanceof Error ? error.message : String(error)}`
      console.error(errorMessage, error)
      healthStore.setError(errorMessage)
      healthStore.setPermissionsGranted(false)
      return { success: false, error: errorMessage }
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore])

  // 获取步数
  const fetchStepCount = useCallback(
    async (date?: Date) => {
      healthStore.setLoading(true)
      healthStore.setError(null)

      try {
        const stepCount: number = await new Promise((resolve, reject) => {
          const stepOptions = {
            date: (date || new Date()).toISOString(),
          }

          AppleHealthKit.getStepCount(stepOptions, (err: string, results: HealthValue) => {
            if (err) {
              reject(new Error(`获取步数时出错: ${err}`))
              return
            }

            const count =
              results && typeof results === "object" && "value" in results
                ? (results as any).value
                : typeof results === "number"
                  ? results
                  : 0

            resolve(count)
          })
        })

        healthStore.setStepCount(stepCount)
        return stepCount
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "获取步数时发生未知错误"
        healthStore.setError(errorMessage)
        throw error
      } finally {
        healthStore.setLoading(false)
      }
    },
    [healthStore, initializeHealthKit],
  )

  // 获取当天的逐搏测量原始数据
  const fetchBeatToBeatData = useCallback(async () => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      // 获取当天的开始和结束时间
      const now = new Date()
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const endOfDay = new Date(now.getTime())

      // 获取原始逐搏数据
      type HeartbeatSample = {
        startDate: string // 开始时间
        endDate: string // 结束时间
        id?: string // 可选ID
        heartbeatSeries?: any // 元数据
      }

      const samples: HeartbeatSample[] = await new Promise((resolve, reject) => {
        AppleHealthKit.getHeartbeatSeriesSamples(
          {
            startDate: startOfDay.toISOString(),
            endDate: endOfDay.toISOString(),
            ascending: true, // 按时间升序排列
          },
          (err: string, results: any[]) => {
            if (err) {
              console.error("获取逐搏测量数据时出错:", err)
              reject(new Error(`获取逐搏测量数据时出错: ${err}`))
              return
            }
            // console.log(`成功获取 ${results?.length || 0} 条逐搏数据`)

            // 转换数据格式以匹配 HeartbeatSample 接口
            const formattedResults: HeartbeatSample[] = (results || []).map((item) => ({
              startDate: item.startDate,
              endDate: item.endDate || item.startDate,
              id: item.id,
              heartbeatSeries: item.heartbeatSeries,
            }))

            resolve(formattedResults)
          },
        )
      })

      // 使用 store 中的方法处理并存储数据
      healthStore.setRMSSDHRVToday(samples)
      return samples
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "获取逐搏测量数据时发生未知错误"
      console.error("获取逐搏测量数据失败:", error)
      healthStore.setError(errorMessage)
      throw error
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore, initializeHealthKit])

  // 获取30天的逐搏测量原始数据（不包括今天的）
  const fetchBeatToBeatDataLast30Days = useCallback(async () => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      const endDate = new Date()
      endDate.setDate(endDate.getDate() - 1) // 设置为昨天
      endDate.setHours(23, 59, 59, 999) // 设置为当天的最后一毫秒

      const startDate = new Date(endDate)
      startDate.setDate(startDate.getDate() - 29) // 30天前（包括今天的前一天）
      startDate.setHours(0, 0, 0, 0) // 设置为当天的开始时间

      const samples: HeartbeatSample[] = await new Promise((resolve, reject) => {
        AppleHealthKit.getHeartbeatSeriesSamples(
          {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            ascending: true, // 按时间升序排列
          },
          (err: string, results: any[]) => {
            if (err) {
              console.error("获取30天逐搏测量数据时出错:", err)
              reject(new Error(`获取30天逐搏测量数据时出错: ${err}`))
              return
            }
            const formattedResults: HeartbeatSample[] = (results || []).map((item) => ({
              startDate: item.startDate,
              endDate: item.endDate || item.startDate,
              id: item.id,
              heartbeatSeries: item.heartbeatSeries,
            }))
            resolve(formattedResults)
          },
        )
      })

      healthStore.setDailyAndAverageRMSSD(samples)
      return samples
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "获取30天逐搏测量数据时发生未知错误"
      console.error("获取30天逐搏测量数据失败:", error)
      healthStore.setError(errorMessage)
      throw error
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore, initializeHealthKit])

  // 获取指定日期的心率数据, 默认为今天
  const fetchHeartRateDay = useCallback(
    async (date?: Date) => {
      healthStore.setLoading(true)
      healthStore.setError(null)

      try {
        const targetDate = date || new Date()
        const startOfDay = new Date(targetDate)
        startOfDay.setHours(0, 0, 0, 0)

        const endOfDay = new Date(targetDate)
        endOfDay.setHours(23, 59, 59, 999)

        const samples: HealthValue[] = await new Promise((resolve, reject) => {
          AppleHealthKit.getHeartRateSamples(
            {
              startDate: startOfDay.toISOString(),
              endDate: endOfDay.toISOString(),
              ascending: true,
            },
            (err: string, results: HealthValue[]) => {
              if (err) {
                console.error("获取心率数据时出错:", err)
                reject(new Error(`获取心率样本时出错: ${err}`))
                return
              }
              resolve(results)
            },
          )
        })

        healthStore.setHeartRateDay(samples)
        return samples
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "获取心率样本时发生未知错误"
        console.error("获取心率数据失败:", error)
        healthStore.setError(errorMessage)
        throw error
      } finally {
        healthStore.setLoading(false)
      } 
    },
    [healthStore, initializeHealthKit],
  )

  // 获取30天前的心率数据（不包括今天）
  const fetchHeartRateLast30Days = useCallback(async () => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      const endDate = new Date()
      endDate.setDate(endDate.getDate() - 1) // 设置为昨天
      endDate.setHours(23, 59, 59, 999) // 设置为当天的最后一毫秒

      const startDate = new Date(endDate)
      startDate.setDate(startDate.getDate() - 29) // 30天前（包括今天的前一天）
      startDate.setHours(0, 0, 0, 0) // 设置为当天的开始时间

      // 1. 获取原始心率数据
      const samples: HealthValue[] = await new Promise((resolve, reject) => {
        AppleHealthKit.getHeartRateSamples(
          {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            ascending: true, // 确保按时间升序排列
          },
          (err: string, results: HealthValue[]) => {
            if (err) {
              reject(new Error(`获取30天心率数据时出错: ${err}`))
              return
            }
            resolve(results)
          },
        )
      })

      // 2. 将原始数据传递给 store 处理
      healthStore.setHeartRateLast30Days(samples)
      return samples
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "获取30天心率数据时发生未知错误"
      healthStore.setError(errorMessage)
      throw error
    }
  }, [healthStore, initializeHealthKit])

  // 获取近30天的静息心率（包括今天）
  const fetchRestingHeartRateLast30Days = useCallback(async () => {
    try {
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 999) // 设置为今天结束时间
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - 59) // 29天前（包括今天共30天）
      startDate.setHours(0, 0, 0, 0) // 设置为当天开始时间

      const samples = await new Promise<HealthValue[]>((resolve, reject) => {
        AppleHealthKit.getRestingHeartRateSamples(
          {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            ascending: false,
          },
          (error: string, results: HealthValue[]) => {
            if (error) {
              console.error("获取静息心率数据时出错:", error)
              reject(new Error("获取静息心率数据失败"))
            } else {
              resolve(results)
            }
          },
        )
      })
      // console.log("🚀 ~ fetchRestingHeartRateLast30Days ~ samples:", samples)
      // 将原始数据传递给 store 处理
      healthStore.setRestingHeartRateLast30Days(samples)
      return samples
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "获取30天静息心率数据时发生未知错误"
      healthStore.setError(errorMessage)
      throw error
    }
  }, [healthStore, initializeHealthKit])

  // 获取近14天的睡眠时心率
  const fetchHeartRateDuringSleep = useCallback(async () => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      // 设置时间范围为最近14天
      const endDate = new Date() // 今天
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 13) // 14天前
      startDate.setHours(0, 0, 0, 0) // 设置为当天的开始

      const sleepSamples: any[] = await new Promise((resolve, reject) => {
        AppleHealthKit.getSleepSamples(
          {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
          (err: string, results: any[]) => {
            if (err) {
              reject(new Error(`获取睡眠数据时出错: ${err}`))
              return
            }
            resolve(results)
          },
        )
      })

      if (sleepSamples.length === 0) {
        console.log("在指定时间范围内未找到睡眠数据")
        healthStore.setHeartRateDuringSleep([])
        return []
      }

      const heartRatePromises = sleepSamples.map(async (sleepSample) => {
        const sleepStartDate = new Date(sleepSample.startDate)
        const sleepEndDate = new Date(sleepSample.endDate)

        const heartRateSamples: HealthValue[] = await new Promise((resolve, reject) => {
          AppleHealthKit.getHeartRateSamples(
            {
              startDate: sleepStartDate.toISOString(),
              endDate: sleepEndDate.toISOString(),
            },
            (err: string, results: HealthValue[]) => {
              if (err) {
                reject(new Error(`获取睡眠期间心率数据时出错: ${err}`))
                return
              }
              resolve(results)
            },
          )
        })
        return heartRateSamples
      })

      const allHeartRates = await Promise.all(heartRatePromises)
      const flattenedHeartRates = allHeartRates.flat()

      // 按时间戳排序（从旧到新）
      flattenedHeartRates.sort(
        (a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
      )

      healthStore.setHeartRateDuringSleep(flattenedHeartRates)
      return flattenedHeartRates
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "获取睡眠时心率数据时发生未知错误"
      console.error("获取睡眠时心率数据失败:", error)
      healthStore.setError(errorMessage)
      throw error
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore, initializeHealthKit])

  // 获取近14天的呼吸率
  const fetchRespiratoryRate = useCallback(async () => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 13)
      startDate.setHours(0, 0, 0, 0)

      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      }

      const samples: HealthValue[] = await new Promise((resolve, reject) => {
        AppleHealthKit.getRespiratoryRateSamples(options, (err: string, results: HealthValue[]) => {
          if (err) {
            reject(new Error(`获取呼吸率数据时出错: ${err}`))
            return
          }
          resolve(results)
        })
      })

      healthStore.setRespiratoryRate(samples)
      return samples
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "获取呼吸率数据时发生未知错误"
      healthStore.setError(errorMessage)
      throw error
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore, initializeHealthKit])

  // 获取近14天的血氧饱和度
  const fetchBloodOxygen = useCallback(async () => {
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 13)
      startDate.setHours(0, 0, 0, 0)

      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      }

      const samples: HealthValue[] = await new Promise((resolve, reject) => {
        AppleHealthKit.getOxygenSaturationSamples(
          options,
          (err: string, results: HealthValue[]) => {
            if (err) {
              reject(new Error(`获取血氧数据时出错: ${err}`))
              return
            }
            resolve(results)
          },
        )
      })

      healthStore.setBloodOxygen(samples)
      return samples
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "获取血氧数据时发生未知错误"
      healthStore.setError(errorMessage)
      throw error
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore, initializeHealthKit])

  // 获取手腕温度数据
  const fetchWristTemperature = useCallback(async () => {
    console.log("开始获取手腕温度数据...")
    healthStore.setLoading(true)
    healthStore.setError(null)

    try {
      // 使用require导入原生模块以避免动态导入问题
      const { WristTemperatureModule } = require("../services/WristTemperatureModule")

      // 检查手腕温度数据可用性
      console.log("检查手腕温度数据可用性...")
      const availability = await WristTemperatureModule.isWristTemperatureAvailable()

      if (!availability.available) {
        const errorMessage = `手腕温度数据不可用: ${availability.reason}`
        console.log(errorMessage)
        healthStore.setError(errorMessage)
        return
      }

      console.log("手腕温度数据可用，开始获取数据...")

      // 获取最近14天的手腕温度数据
      const samples = await WristTemperatureModule.getRecentWristTemperature(14)

      if (!samples || samples.length === 0) {
        console.log("未找到手腕温度数据")

        const noDataMessage = `未找到手腕温度数据。可能的原因：

1. 设备要求：需要 Apple Watch Series 8 或更新版本
2. 睡眠跟踪：确保在 Apple Watch 应用中启用了睡眠跟踪
3. 佩戴设置：确保在睡眠期间佩戴 Apple Watch
4. 数据收集：手腕温度数据仅在睡眠期间收集
5. 时间要求：可能需要几晚的数据才能显示
6. 权限设置：检查健康应用中的数据访问权限

请检查以上设置并等待数据收集。`

        healthStore.setError(noDataMessage)
        return
      }

      console.log(`成功获取 ${samples.length} 条手腕温度数据`)

      // 转换数据格式以匹配现有的数据结构
      const wristTemperatureData = samples.map((sample) => ({
        value: sample.value,
        startDate: sample.startDate,
        endDate: sample.endDate || sample.startDate,
        date: sample.startDate,
        unit: "celsius" as const,
      }))

      // 存储到 store
      healthStore.setWristTemperature(wristTemperatureData)
      console.log("手腕温度数据已存储到 store")
    } catch (error) {
      console.error("获取手腕温度数据失败:", error)

      // 提供更友好的错误信息
      let errorMessage = "获取手腕温度数据时发生错误"

      if (error instanceof Error) {
        if (error.message.includes("permission") || error.message.includes("denied")) {
          errorMessage = "手腕温度数据访问权限被拒绝。请在设置 > 隐私与安全性 > 健康 中允许访问。"
        } else if (error.message.includes("unavailable") || error.message.includes("unsupported")) {
          errorMessage =
            "您的设备不支持手腕温度数据。需要 Apple Watch Series 8 或更新版本，以及 iOS 16.0 或更高版本。"
        } else if (error.message.includes("WristTemperatureModule")) {
          errorMessage = "手腕温度模块未正确配置。请确保应用已正确构建并包含原生模块。"
        } else {
          errorMessage = `获取手腕温度数据失败: ${error.message}`
        }
      }

      healthStore.setError(errorMessage)
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore])

  // 获取用户基本信息
  const fetchUserInfo = useCallback(async () => {
    try {
      healthStore.setLoading(true)

      // 1. 检查并请求权限
      // 权限现在由外部统一管理，这里不再重复请求

      // 2. 获取用户信息
      // 2.1 获取性别（使用回调）
      const gender = await new Promise<{ value: string | null }>((resolve) => {
        // 定义性别类型
        type BiologicalSex = "male" | "female" | "other" | "unknown"

        // 使用 any 类型绕过类型检查，因为 react-native-health 的类型定义不准确
        AppleHealthKit.getBiologicalSex({}, (err: string, results: any) => {
          if (err) {
            console.error("获取性别失败:", err)
            resolve({ value: null })
            return
          }
          // 确保结果值在预期范围内
          const validGenders: BiologicalSex[] = ["male", "female", "other", "unknown"]
          const genderValue = validGenders.includes(results.value)
            ? (results.value as BiologicalSex)
            : null
          resolve({ value: genderValue })
        })
      })

      // 2.2 获取出生日期（使用回调）
      const dateOfBirth = await new Promise<{ value: string | null }>((resolve) => {
        // 使用 any 类型绕过类型检查，因为 react-native-health 的类型定义不准确
        AppleHealthKit.getDateOfBirth({}, (err: string, results: any) => {
          if (err) {
            console.error("获取出生日期失败:", err)
            resolve({ value: null })
            return
          }
          // 确保结果值存在且是字符串类型
          const dateValue = typeof results?.value === "string" ? results.value : null
          resolve({ value: dateValue })
        })
      })

      // 2.3 获取身高和体重（异步方法）
      const [height, weight] = await Promise.all([
        new Promise<{ value: number | null }>((resolve) => {
          AppleHealthKit.getLatestHeight(
            { unit: "cm" as any }, // 使用类型断言解决类型问题
            (err: string, results: HealthValue) => {
              if (err) {
                console.error("获取身高失败:", err)
                resolve({ value: null })
                return
              }
              resolve({ value: results.value })
            },
          )
        }),
        new Promise<{ value: number | null }>((resolve) => {
          AppleHealthKit.getLatestWeight(
            { unit: "kg" as any }, // 使用类型断言解决类型问题
            (err: string, results: HealthValue) => {
              if (err) {
                console.error("获取体重失败:", err)
                resolve({ value: null })
                return
              }
              resolve({ value: results.value })
            },
          )
        }),
      ])

      // 3. 计算年龄
      let age = null
      if (dateOfBirth?.value) {
        const today = new Date()
        const birthDate = new Date(dateOfBirth.value)
        let calculatedAge = today.getFullYear() - birthDate.getFullYear()
        const monthDiff = today.getMonth() - birthDate.getMonth()

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          calculatedAge--
        }

        age = calculatedAge > 0 ? calculatedAge : null
      }

      // 4. 保存用户信息到 store
      healthStore.setUserInfo({
        gender: gender?.value || null,
        age: age,
        height: height?.value || null,
        weight: weight?.value || null,
      })

      console.log("用户信息获取成功:", {
        gender: gender?.value,
        age,
        height: height?.value,
        weight: weight?.value,
      })

      return {
        gender: gender?.value,
        age,
        height: height?.value || null,
        weight: weight?.value || null,
      }
    } catch (error) {
      console.error("获取用户信息失败:", error)
      healthStore.setError("获取用户信息失败")
      throw error
    } finally {
      healthStore.setLoading(false)
    }
  }, [healthStore, initializeHealthKit])

  return {
    fetchStepCount,
    fetchHeartRateDay,
    fetchHeartRateLast30Days,
    fetchRestingHeartRateLast30Days,
    fetchBeatToBeatData,
    fetchBeatToBeatDataLast30Days,
    initializeHealthKit,
    fetchUserInfo, // 添加 fetchUserInfo 方法到返回对象
    fetchHeartRateDuringSleep,
    fetchRespiratoryRate,
    fetchBloodOxygen,
    fetchWristTemperature,
  }
}

export default useHealthData
