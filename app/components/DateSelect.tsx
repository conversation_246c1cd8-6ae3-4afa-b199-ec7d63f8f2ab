import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Text } from "./Text"
import { PressableIcon } from "./Icon"
import { aw, af } from "@/utils/adaptiveSize"
import { useAppTheme } from "@/utils/useAppTheme"

export type DateMode = "day" | "week" | "month" | "year"

interface DateHeaderProps {
  mode: DateMode
  value: Date
  onChange: (newValue: Date) => void
  style?: ViewStyle
  textStyle?: ViewStyle
}

function formatDate(mode: DateMode, value: Date): string {
  if (!value) return ""
  const y = value.getFullYear()
  const m = (value.getMonth() + 1).toString().padStart(2, "0")
  const d = value.getDate().toString().padStart(2, "0")
  if (mode === "day") return `${m}/${d}`
  if (mode === "month") return `${y}/${m}`
  if (mode === "year") return `${y}`
  if (mode === "week") {
    // 取本周周一和周日
    const day = value.getDay() || 7
    const monday = new Date(value)
    monday.setDate(value.getDate() - day + 1)
    const sunday = new Date(monday)
    sunday.setDate(monday.getDate() + 6)
    const m1 = (monday.getMonth() + 1).toString().padStart(2, "0")
    const d1 = monday.getDate().toString().padStart(2, "0")
    const m2 = (sunday.getMonth() + 1).toString().padStart(2, "0")
    const d2 = sunday.getDate().toString().padStart(2, "0")
    return `${m1}/${d1}-${m2}/${d2}`
  }
  return ""
}

function getNext(mode: DateMode, value: Date): Date {
  const d = new Date(value)
  if (mode === "day") d.setDate(d.getDate() + 1)
  if (mode === "week") d.setDate(d.getDate() + 7)
  if (mode === "month") d.setMonth(d.getMonth() + 1)
  if (mode === "year") d.setFullYear(d.getFullYear() + 1)
  return d
}
function getPrev(mode: DateMode, value: Date): Date {
  const d = new Date(value)
  if (mode === "day") d.setDate(d.getDate() - 1)
  if (mode === "week") d.setDate(d.getDate() - 7)
  if (mode === "month") d.setMonth(d.getMonth() - 1)
  if (mode === "year") d.setFullYear(d.getFullYear() - 1)
  return d
}

/**
 * 日期头部组件
 * 显示当前日期，用于各个详情页面
 */
export const DateSelect: FC<DateHeaderProps> = ({ mode, value, onChange, style, textStyle }) => {
  const theme = useAppTheme()
  const isToday = () => {
    if (!value) return false
    const now = new Date()
    if (mode === "day") {
      return value.getFullYear() === now.getFullYear() && value.getMonth() === now.getMonth() && value.getDate() === now.getDate()
    }
    if (mode === "week") {
      // 本周的周一
      const day = now.getDay() || 7
      const monday = new Date(now)
      monday.setDate(now.getDate() - day + 1)
      // value的周一
      const vday = value.getDay() || 7
      const vmonday = new Date(value)
      vmonday.setDate(value.getDate() - vday + 1)
      return vmonday.getFullYear() === monday.getFullYear() && vmonday.getMonth() === monday.getMonth() && vmonday.getDate() === monday.getDate()
    }
    if (mode === "month") {
      return value.getFullYear() === now.getFullYear() && value.getMonth() === now.getMonth()
    }
    if (mode === "year") {
      return value.getFullYear() === now.getFullYear()
    }
    return false
  }
  const $styles = {
    container: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: aw(4),
      ...style,
    } as ViewStyle,
    icon: {
      paddingHorizontal: aw(12),
    } as ViewStyle,
    dateText: {
      textAlign: 'center' as const,
      fontSize: af(16),
      lineHeight: aw(24),
      color: theme.theme.colors.palette.primary500,
      fontFamily: theme.theme.typography.primary.bold,
      minWidth: aw(120),
      ...textStyle,
    } as ViewStyle,
  }
  return (
    <View style={$styles.container}>
      <PressableIcon icon="caretLeft" size={16} onPress={() => onChange(getPrev(mode, value))} />
      <Text style={$styles.dateText}>{formatDate(mode, value)}</Text>
      {!isToday() ? (
        <PressableIcon icon="caretRight" size={16} onPress={() => onChange(getNext(mode, value))} />
      ) : (
        <View style={{ width: aw(16) }} />
      )}
    </View>
  )
}