import { FC, useState } from "react"
import {
  Modal,
  TouchableOpacity,
  View,
  ViewStyle,
  TextStyle,
} from "react-native"
import { Icon, IconTypes, Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, ah, af } from "@/utils/adaptiveSize"
import { colors } from "@/theme"

interface InfoIconProps {
  /** 图标类型 */
  icon?: IconTypes
  /** 图标大小 */
  size?: number
  /** 图标颜色 */
  color?: string
  /** 是否使用原始颜色 */
  useOriginalColor?: boolean
  /** 推窗标题 */
  title?: string
  /** 要显示的内容文字 */
  content: string
  /** 自定义样式 */
  style?: ViewStyle
}

export const InfoIcon: FC<InfoIconProps> = ({
  icon = "info",
  size = aw(16),
  color = "#BCBFD0",
  useOriginalColor = false,
  title = "指标说明",
  content,
  style,
}) => {
  const [modalVisible, setModalVisible] = useState(false)
  const theme = useAppTheme()

  const handlePress = () => {
    setModalVisible(true)
  }

  const handleClose = () => {
    setModalVisible(false)
  }

  const $styles = {
    container: {
      // 保持原有Icon的布局
    } as ViewStyle,

    modalOverlay: {
      flex: 1,
      justifyContent: 'flex-end',
    } as ViewStyle,

    modalContent: {
      backgroundColor: colors.palette.neutral100,
      borderTopLeftRadius: aw(20),
      borderTopRightRadius: aw(20),
      paddingHorizontal: aw(24),
      paddingTop: ah(24),
      paddingBottom: ah(34),
      maxHeight: '70%',
    } as ViewStyle,

    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: ah(16),
    } as ViewStyle,

    modalTitle: {
      fontSize: af(18),
      lineHeight: af(24),
      fontWeight: '600',
      color: theme.theme.colors.text,
      flex: 1,
    } as TextStyle,

    closeButton: {
      padding: aw(8),
      marginRight: -aw(8),
    } as ViewStyle,

    modalText: {
      fontSize: af(16),
      lineHeight: af(24),
      color: theme.theme.colors.text,
      paddingBottom: ah(30),
    } as TextStyle,
  }

  return (
    <>
      <TouchableOpacity
        style={style}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Icon
          icon={icon}
          size={size}
          color={color}
          useOriginalColor={useOriginalColor}
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={handleClose}
      >
        <View style={$styles.modalOverlay}>
          <View style={$styles.modalContent}>
            <View style={$styles.modalHeader}>
              <Text style={$styles.modalTitle}>{title}</Text>
              <TouchableOpacity
                style={$styles.closeButton}
                onPress={handleClose}
                activeOpacity={0.7}
              >
                <Icon icon="x" size={aw(20)} color={theme.theme.colors.textDim} />
              </TouchableOpacity>
            </View>
            <Text style={$styles.modalText}>{content}</Text>
          </View>
        </View>
      </Modal>
    </>
  )
}