import { useState } from "react"
import { Dimensions, TouchableOpacity, View, ViewStyle, TextStyle, Platform } from "react-native"
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated"
import { aw } from "@/utils/adaptiveSize"
import { Text } from "@/components"

const { width } = Dimensions.get("window")
const TABS = ["日", "周", "月", "年"]
const TAB_WIDTH = (width - 40) / TABS.length

interface TabsProps {
  activeTab?: number
  onTabChange?: (index: number) => void
}

export const Tabs = ({ activeTab: propActiveTab, onTabChange }: TabsProps) => {
  const [internalActiveTab, setInternalActiveTab] = useState(0)
  const activeTab = propActiveTab !== undefined ? propActiveTab : internalActiveTab
  const translateX = useSharedValue(0)

  const handlePress = (index: number) => {
    if (propActiveTab === undefined) {
      setInternalActiveTab(index)
    }
    if (onTabChange) {
      onTabChange(index)
    }
    if (Platform.OS !== "web") {
      translateX.value = withTiming(index * TAB_WIDTH, { duration: 300 })
    }
  }

  const animatedStyle = useAnimatedStyle(() => {
    if (Platform.OS === "web") {
      return { left: activeTab * TAB_WIDTH }
    }
    return {
      transform: [{ translateX: translateX.value }],
    }
  })

  const $styles = {
    container: {
      flexDirection: "row",
      padding: 4,
      position: "relative",
      height: aw(40),
      backgroundColor: "#E0DDE4",
      borderRadius: aw(8),
      zIndex: -1,
    } as ViewStyle,

    activeTabText: {
      color: "#23235f",
      fontWeight: "bold",
      fontSize: aw(16),
      lineHeight: aw(24),
      zIndex: 100,
    } as TextStyle,
    animatedView: {
      backgroundColor: "#fff",
      borderRadius: aw(8),
      height: aw(32),
      position: "absolute",
      width: TAB_WIDTH,
      marginLeft: 4,
      top: 4,
      // left: 4,
    } as ViewStyle,

    tab: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
      height: aw(32),
    } as ViewStyle,
    tabText: {
      color: "#8c8cb2",
      fontWeight: "bold",
      fontSize: aw(16),
      lineHeight: aw(24),
      zIndex: 2,
    } as TextStyle,
  }

  return (
    <View style={$styles.container}>
      <Animated.View style={[$styles.animatedView, animatedStyle]} />
      {TABS.map((tab, index) => (
        <TouchableOpacity key={tab} onPress={() => handlePress(index)} style={$styles.tab}>
          <Text style={[$styles.tabText, activeTab === index && $styles.activeTabText]}>{tab}</Text>
        </TouchableOpacity>
      ))}
    </View>
  )
}