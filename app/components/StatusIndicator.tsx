import { View, ViewStyle, TextStyle } from "react-native"
import { aw } from "@/utils/adaptiveSize"
import { Text } from "@/components/Text"
import { Icon } from "@/components/Icon"

interface StatusIndicatorProps {
  /**
   * 状态类型：normal（正常）或 error（异常）
   */
  status: 'normal' | 'error'

  /**
   * 自定义样式
   */
  style?: ViewStyle

  /**
   * 图标大小
   */
  iconSize?: number

  /**
   * 文字样式
   */
  textStyle?: TextStyle
}

export const StatusIndicator = ({
  status,
  style,
  iconSize = aw(12),
  textStyle
}: StatusIndicatorProps) => {
  const isNormal = status === 'normal'

  const $styles = {
    container: {
      flexDirection: "row",
      alignItems: "center",
      ...style,
    } as ViewStyle,

    statusText: {
      fontSize: aw(12),
      color: isNormal ? '#6892D5' : '#E84A5F',
      marginLeft: aw(4),
      ...textStyle,
    } as TextStyle,
  }

  return (
    <View style={$styles.container}>
      <Icon
        icon={isNormal ? "indicatorNormal" : "indicatorError"}
        size={iconSize}
        useOriginalColor
      />
      <Text
        style={$styles.statusText}
        text={isNormal ? "正常" : "异常"}
      />
    </View>
  )
}