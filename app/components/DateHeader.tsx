import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Text } from "./Text"
import { getCurrentDate } from "@/utils/formatDate"
import { useAppTheme } from "@/utils/useAppTheme"
import { aw, af } from "@/utils/adaptiveSize"

interface DateHeaderProps {
  /**
   * 自定义样式
   */
  style?: ViewStyle
  /**
   * 自定义日期文本样式
   */
  textStyle?: ViewStyle
}

/**
 * 日期头部组件
 * 显示当前日期，用于各个详情页面
 */
export const DateHeader: FC<DateHeaderProps> = ({ style, textStyle }) => {
  const theme = useAppTheme()

  const $styles = {
    dateContainer: {
      marginTop: aw(-19),
      ...style,
    } as ViewStyle,

    dateText: {
      textAlign: 'center' as const,
      fontSize: af(11),
      lineHeight: aw(12),
      color: theme.theme.colors.palette.primary100,
      fontFamily: theme.theme.typography.primary.bold,
      ...textStyle,
    } as ViewStyle,
  }

  return (
    <View style={$styles.dateContainer}>
      <Text style={$styles.dateText}>{getCurrentDate()}</Text>
    </View>
  )
}