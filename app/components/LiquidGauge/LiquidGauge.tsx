import { aw } from '@/utils';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { View, ViewStyle } from 'react-native';
import Svg, {
  Circle,
  ClipPath,
  Defs,
  LinearGradient,
  Path,
  Stop,
  Text as SvgText,
} from 'react-native-svg';

interface LiquidGaugeProps {
  value: number;
  unit?: string;
  size?: number;
}

const LiquidGauge: FC<LiquidGaugeProps> = ({
  value,
  unit = 'ms',
  size = 200,
}) => {
  const $styles = createStyles();

  // 计算水球图的相关参数
  const [phase, setPhase] = useState(0);
  const animationRef = useRef<number>();

  // 计算水球的各种参数
  const { radius, centerX, centerY, waveOffset, waveHeight } = useMemo(() => {
    // 水球半径，基于size按比例计算
    const radius = size * 0.4;
    // 水球中心点X坐标
    const centerX = size / 2;
    // 水球中心点Y坐标
    const centerY = size / 2;

    // 将输入值标准化到0-1范围
    const normalizedValue = Math.min(100, Math.max(0, value)) / 100;

    // 计算波浪的垂直偏移量，控制水位高度
    // 1. (1 - normalizedValue): 反转值，使值越大水位越高
    // 2. size * 0.9: 控制水位的最大范围
    const waveOffset = (1 - normalizedValue) * size * 0.9;

    // 波浪的基础高度，影响波浪的振幅
    // 值越大，波浪越明显，但不要超过水球半径的1/4
    const waveHeight = size * 0.015;

    return { radius, centerX, centerY, waveOffset, waveHeight };
  }, [value, size]);

  // 动画效果
  useEffect(() => {
    let startTime: number | null = null;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;

      // 更新相位（控制波浪移动）
      // 1. elapsed / 1000: 将毫秒转换为秒
      // 2. Math.PI * 2: 一个完整的正弦波周期（2π）
      // 3. 增大分母可以减慢动画速度，增大分子可以加快速度
      setPhase((elapsed / 1800) * Math.PI * 2);

      // 继续动画
      animationRef.current = requestAnimationFrame(animate);
    };

    // 启动动画
    animationRef.current = requestAnimationFrame(animate);

    // 清理函数
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  /**
   * 生成波浪路径
   * @param offset 波浪的垂直偏移量，控制水位高度（值越小水位越高）
   * @param height 波浪的振幅，控制波浪高度
   * @param phaseOffset 相位偏移，用于创建多个波浪的错位效果
   */
  const generateWavePath = (offset: number, height: number, phaseOffset: number = 0) => {
    // 波浪的宽度，使用完整的水球宽度
    const waveWidth = size;
    // 波浪的分段数，值越大波浪越平滑但性能消耗越大
    const segments = 40;
    // 每段的宽度
    const segmentWidth = waveWidth / segments;

    // 计算波浪的起始Y坐标，确保不会超出水球底部
    const startY = Math.min(offset + height, size);

    // 开始路径，移动到起点
    let path = `M 0 ${startY}`;

    // 生成波浪曲线
    for (let i = 0; i <= segments; i++) {
      const x = i * segmentWidth;
      /**
       * 波浪曲线计算公式：
       * 1. (x / waveWidth) * Math.PI * 4: 控制波浪的周期
       *    - 4 表示波浪的周期数，值越大波浪越密集
       * 2. phase: 动画相位，使波浪产生移动效果
       * 3. phaseOffset: 相位偏移，用于创建多个波浪的错位效果
       * 4. height: 波浪的振幅，控制波浪高度
       */
      const y = offset + Math.sin((x / waveWidth) * Math.PI * 4 + phase + phaseOffset) * height;
      path += ` L ${x} ${y}`;
    }

    // 闭合路径，形成封闭区域
    const endY = Math.min(offset + height, size);
    path += ` L ${size} ${endY} L ${size} ${size} L 0 ${size} Z`;

    return path;
  };

  // 主波浪（深色 #9896F1）
  const mainWavePath = useMemo(
    // waveHeight * 1.5: 主波浪的振幅是基础值的1.5倍
    () => generateWavePath(waveOffset, waveHeight * 1.5),
    [waveOffset, waveHeight, size, phase]
  );

  // 次波浪（浅色 #BCBAFA）
  const secondaryWavePath = useMemo(
    // 增加相位偏移到180度，使次波浪与主波浪完全错开
    () => generateWavePath(waveOffset, waveHeight, Math.PI),
    [waveOffset, waveHeight, size, phase]
  );

  return (
    <View style={[$styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size}>
        <Defs>
          {/* 水球渐变背景 */}
          <LinearGradient id="liquidGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <Stop offset="100%" stopColor="#9896F1" />
            <Stop offset="0%" stopColor="#BCBAFA" />
          </LinearGradient>

          {/* 水球外边框 */}
          <LinearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#fff" />
            <Stop offset="100%" stopColor="#f0f0f0" />
          </LinearGradient>
        </Defs>

        {/* 水球背景 */}
        <Circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="#D0CFFA"
          stroke="#fff"
          strokeWidth={8}
        />

        {/* 水波效果 - 次波浪（浅色） */}
        <Circle
          cx={centerX}
          cy={centerY}
          r={radius * 0.9}
          fill="#BCBAFA"
          fillOpacity={0.8}
          clipPath="url(#secondaryWaveClip)"
        />

        {/* 水波效果 - 主波浪（深色） */}
        <Circle
          cx={centerX}
          cy={centerY}
          r={radius * 0.9}
          fill="#9896F1"
          clipPath="url(#mainWaveClip)"
        />

        {/* 水波遮罩 */}
        <Defs>
          <ClipPath id="mainWaveClip">
            <Path d={mainWavePath} fill="#fff" />
          </ClipPath>
          <ClipPath id="secondaryWaveClip">
            <Path d={secondaryWavePath} fill="#fff" />
          </ClipPath>
        </Defs>

        {/* 显示数值 */}
        <SvgText
          x={centerX}
          y={centerY}
          fill="#fff"
          fontSize={aw(54)}
          fontWeight="bold"
          textAnchor="middle"
          alignmentBaseline="middle"
        >
          {Math.round(value)}
        </SvgText>

        {/* 单位 */}
        <SvgText
          x={centerX}
          y={centerY + size * 0.18}
          fill="#fff"
          fontSize={14}
          fontWeight="bold"
          textAnchor="middle"
        >
          {unit}
        </SvgText>
      </Svg>
    </View>
  );
};

const createStyles = (): { container: ViewStyle } => ({
  container: {
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: 'transparent',
  },
});

export { LiquidGauge };
