import {
  Image,
  ImageStyle,
  StyleProp,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewProps,
  ViewStyle,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"

export type IconTypes = keyof typeof iconRegistry

type BaseIconProps = {
  /**
   * The name of the icon
   */
  icon: IconTypes

  /**
   * An optional tint color for the icon
   */
  color?: string

  /**
   * Whether to use the original color of the icon image without any tint
   */
  useOriginalColor?: boolean

  /**
   * An optional size for the icon. If not provided, the icon will be sized to the icon's resolution.
   */
  size?: number

  /**
   * Style overrides for the icon image
   */
  style?: StyleProp<ImageStyle>

  /**
   * Style overrides for the icon container
   */
  containerStyle?: StyleProp<ViewStyle>
}

type PressableIconProps = Omit<TouchableOpacityProps, "style"> & BaseIconProps
type IconProps = Omit<ViewProps, "style"> & BaseIconProps

/**
 * A component to render a registered icon.
 * It is wrapped in a <TouchableOpacity />
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Icon/}
 * @param {PressableIconProps} props - The props for the `PressableIcon` component.
 * @returns {JSX.Element} The rendered `PressableIcon` component.
 */
export function PressableIcon(props: PressableIconProps) {
  const {
    icon,
    color,
    size,
    style: $imageStyleOverride,
    containerStyle: $containerStyleOverride,
    ...pressableProps
  } = props

  const { theme } = useAppTheme()

  const $imageStyle: StyleProp<ImageStyle> = [
    $imageStyleBase,
    { tintColor: color ?? theme.colors.text },
    size !== undefined && { width: size, height: size },
    $imageStyleOverride,
  ]

  return (
    <TouchableOpacity {...pressableProps} style={$containerStyleOverride}>
      <Image style={$imageStyle} source={iconRegistry[icon]} />
    </TouchableOpacity>
  )
}

/**
 * A component to render a registered icon.
 * It is wrapped in a <View />, use `PressableIcon` if you want to react to input
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Icon/}
 * @param {IconProps} props - The props for the `Icon` component.
 * @returns {JSX.Element} The rendered `Icon` component.
 */
export function Icon(props: IconProps) {
  const {
    icon,
    color,
    useOriginalColor,
    size,
    style: $imageStyleOverride,
    containerStyle: $containerStyleOverride,
    ...viewProps
  } = props

  const { theme } = useAppTheme()

  const $imageStyle: StyleProp<ImageStyle> = [
    $imageStyleBase,
    !useOriginalColor && { tintColor: color ?? theme.colors.text },
    size !== undefined && { width: size, height: size },
    $imageStyleOverride,
  ].filter(Boolean)

  return (
    <View {...viewProps} style={$containerStyleOverride}>
      <Image style={$imageStyle} source={iconRegistry[icon]} />
    </View>
  )
}

export const iconRegistry = {
  back: require("../../assets/icons/back.png"),
  bed: require("../../assets/icons/bed.png"),
  bell: require("../../assets/icons/bell.png"),
  caretLeft: require("../../assets/icons/caretLeft.png"),
  caretRight: require("../../assets/icons/caretRight.png"),
  check: require("../../assets/icons/check.png"),
  hidden: require("../../assets/icons/hidden.png"),
  ladybug: require("../../assets/icons/ladybug.png"),
  lock: require("../../assets/icons/lock.png"),
  menu: require("../../assets/icons/menu.png"),
  more: require("../../assets/icons/more.png"),
  settings: require("../../assets/icons/settings.png"),
  view: require("../../assets/icons/view.png"),
  x: require("../../assets/icons/x.png"),
  statusExcellent: require("../../assets/icons/status_excellent.png"),
  statusNormal: require("../../assets/icons/status_normal.png"),
  stressOverload: require("../../assets/icons/stress_overload.png"),
  stressWarning: require("../../assets/icons/stress_warning.png"),
  restingHeartRate: require("../../assets/icons/resting_heart_rate.png"),
  breathingRate: require("../../assets/icons/breathing_rate.png"),
  wristTemperature: require("../../assets/icons/wrist_temperature.png"),
  sleepEfficiency: require("../../assets/icons/sleep_efficiency.png"),
  sleepTime: require("../../assets/icons/sleepTime.png"),
  bloodOxygen: require("../../assets/icons/blood_oxygen.png"),
  indicatorError: require("../../assets/icons/indicator_error.png"),
  indicatorNormal: require("../../assets/icons/indicator_normal.png"),
  fitness: require("../../assets/icons/fitness.png"),
  bask: require("../../assets/icons/bask.png"),
  info: require("../../assets/icons/info.png"),
  arrowUp: require("../../assets/icons/arrow_up.png"),
  arrowDown: require("../../assets/icons/arrow_down.png"),
  stepIcon: require("../../assets/icons/stepIcon.png"),
  tabbarHome: require("../../assets/icons/tabbar_home.png"),
  tabbarActivity: require("../../assets/icons/tabbar_activity.png"),
  tabbarTrend: require("../../assets/icons/tabbar_trend.png"),
  tabbarSetting: require("../../assets/icons/tabbar_Steeing.png"),
  dialComponent: require("../../assets/icons/dial_component.png"),
  faq: require("../../assets/icons/faq.png"),
  share: require("../../assets/icons/share.png"),
  personalInfo: require("../../assets/icons/personal_info.png"),
  aboutUs: require("../../assets/icons/about_us.png"),
  praise: require("../../assets/icons/praise.png"),
  contactUs: require("../../assets/icons/contact_us.png"),
  notification: require("../../assets/icons/notification.png"),
  appearance: require("../../assets/icons/appearance.png"),
  language: require("../../assets/icons/language.png"),
  vibrationFeedback: require("../../assets/icons/vibration_feedback.png"),
  hrvRefreshMode: require("../../assets/icons/hrv_refresh_mode.png"),
}

const $imageStyleBase: ImageStyle = {
  resizeMode: "contain",
}
