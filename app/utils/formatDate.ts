// Note the syntax of these imports from the date-fns library.
// If you import with the syntax: import { format } from "date-fns" the ENTIRE library
// will be included in your production bundle (even if you only use one function).
// This is because react-native does not support tree-shaking.
import { type Locale } from "date-fns/locale"
import { format } from "date-fns/format"
import { parseISO } from "date-fns/parseISO"
import { getLocale } from "../i18n/i18n"

type Options = Parameters<typeof format>[2]

export let dateFnsLocale: Locale
export const loadDateFnsLocale = () => {
  switch (getLocale()) {
    case "en":
      dateFnsLocale = require("date-fns/locale/en-US").default
      break
    case "zh":
      dateFnsLocale = require("date-fns/locale/zh-CN").default
      break
    default:
      dateFnsLocale = require("date-fns/locale/en-US").default
  }
}

export const formatDate = (date: string, dateFormat?: string, options?: Options) => {
  const dateOptions = {
    ...options,
    locale: dateFnsLocale,
  }
  return format(parseISO(date), dateFormat ?? "MMM dd, yyyy", dateOptions)
}

/**
 * 获取当前日期并格式化为指定格式
 * @param dateFormat 日期格式，默认为 "yyyy/MM/dd"
 * @param options 格式化选项
 * @returns 格式化后的当前日期字符串
 */
export const getCurrentDate = (dateFormat: string = "yyyy/MM/dd", options?: Options) => {
  const dateOptions = {
    ...options,
    locale: dateFnsLocale,
  }
  return format(new Date(), dateFormat, dateOptions)
}

/**
 * 解析日期字符串为 Date 对象
 * @param dateString 日期字符串，支持多种格式
 * @returns 解析后的 Date 对象，如果解析失败则返回 null
 */
export const parseDate = (dateString: string): Date | null => {
  if (!dateString) return null
  
  // 处理 YYYY-MM 格式
  if (/^\d{4}-\d{1,2}$/.test(dateString)) {
    return new Date(`${dateString}-01`)
  }
  
  // 处理 YYYY-MM-DD 格式
  if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(dateString)) {
    return new Date(dateString)
  }
  
  // 处理其他格式
  const date = new Date(dateString)
  return isNaN(date.getTime()) ? null : date
}

/**
 * 从日期字符串中提取月份
 * @param dateString 日期字符串
 * @returns 月份数字（1-12），如果解析失败则返回 null
 */
export const extractMonth = (dateString: string): number | null => {
  const date = parseDate(dateString)
  return date ? date.getMonth() + 1 : null
}

/**
 * 将日期格式化为 YYYY-MM-DD HH:mm:ss 格式
 * @param date 可以是 Date 对象或日期字符串
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  return format(dateObj, 'yyyy-MM-dd HH:mm:ss', { locale: dateFnsLocale })
}

/**
 * 将 Date 对象或日期字符串安全地格式化为 'yyyy-MM-dd' 格式，始终使用本地时区。
 * @param date Date 对象或有效的日期字符串
 * @returns 'yyyy-MM-dd' 格式的字符串
 */
export const formatDateToYyyyMmDd = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'yyyy-MM-dd', { locale: dateFnsLocale });
};
