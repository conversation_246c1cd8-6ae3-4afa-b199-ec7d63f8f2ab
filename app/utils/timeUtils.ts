/**
 * 时间转换和时间段判断工具函数
 */
export const timeUtils = {
  /**
   * 将时间字符串转换为分钟数以便比较
   * @param time 时间字符串，格式为 "HH:MM"
   * @returns 分钟数
   */
  timeToMinutes: (time: string): number => {
    const [hours, minutes] = time.split(":").map(Number)
    return hours * 60 + minutes
  },

  /**
   * 判断当前时间是否在指定时间段内
   * @param currentTime 当前时间字符串
   * @param timeRange 时间段范围 [开始时间, 结束时间]
   * @returns 是否在指定时间段内
   */
  isInTimeRange: (currentTime: string, timeRange: [string, string]): boolean => {
    const currentMinutes = timeUtils.timeToMinutes(currentTime)
    const [startTime, endTime] = timeRange
    const startMinutes = timeUtils.timeToMinutes(startTime)
    let endMinutes = timeUtils.timeToMinutes(endTime)

    // 处理跨天的时间段（如 22:00 - 06:00）
    if (endMinutes <= startMinutes) {
      endMinutes += 24 * 60
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes % (24 * 60)
    }

    return currentMinutes >= startMinutes && currentMinutes <= endMinutes
  },
}
