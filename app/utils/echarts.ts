// 导入 echarts 核心模块
import * as echarts from "echarts/core"
// 导入图表类型
import { LineChart, BarChart, GaugeChart, CustomChart, ScatterChart } from "echarts/charts"
// 导入组件
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  GraphicComponent,
} from "echarts/components"
// 导入渲染器
import { SVGRenderer } from "@wuba/react-native-echarts/svgChart"

// 注册必须的组件
echarts.use([
  // 渲染器
  SVGRenderer,

  // 图表类型
  LineChart,
  BarChart,
  GaugeChart,
  CustomChart,
  ScatterChart,

  // 组件
  GridComponent,
  TooltipComponent,
  LegendComponent,
  GraphicComponent,
])

// 导出 echarts 实例
export default echarts
