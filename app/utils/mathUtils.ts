/**
 * 数学计算工具函数
 */

/**
 * 计算静息心率范围
 * @param data 包含 value 属性的对象数组
 * @returns 返回心率范围 [min, max]
 */
export const calculateRestingHRRange = <T extends { value: number }>(
  data: T[],
): [number, number] => {
  if (data.length === 0) {
    return [0, 0]
  }

  // 计算平均值
  const mean = data.reduce((sum, item) => sum + item.value, 0) / data.length

  // 计算标准差
  const variance = data.reduce((sum, item) => sum + Math.pow(item.value - mean, 2), 0) / data.length
  const std = Math.sqrt(variance)

  // 返回范围 [平均值-标准差, 平均值+标准差]
  return [parseFloat((mean - std).toFixed(1)), parseFloat((mean + std).toFixed(1))]
}

/**
 * 计算数字数组的平均值
 * @param values 数字数组
 * @returns 平均值
 */
export const calculateMean = (values: number[]): number => {
  if (values.length === 0) return 0
  return values.reduce((sum, val) => sum + val, 0) / values.length
}

/**
 * 计算数字数组的标准差
 * @param values 数字数组
 * @returns 标准差
 */
export const calculateStandardDeviation = (values: number[]): number => {
  if (values.length === 0) return 0
  const mean = calculateMean(values)
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  return Math.sqrt(variance)
}
