import { NativeModules } from 'react-native'

/**
 * 手腕温度数据接口
 */
export interface WristTemperatureSample {
  id: string
  value: number // 温度值（摄氏度）
  startDate: string // ISO格式日期字符串
  endDate: string // ISO格式日期字符串
  unit: 'celsius'
  metadata: Record<string, any>
}

/**
 * 查询选项接口
 */
export interface WristTemperatureQueryOptions {
  startDate?: string // ISO格式日期字符串
  endDate?: string // ISO格式日期字符串
  limit?: number // 结果数量限制
  ascending?: boolean // 排序方式
}

/**
 * 可用性检查结果接口
 */
export interface WristTemperatureAvailability {
  available: boolean
  reason: string
}

/**
 * 手腕温度原生模块接口
 */
interface WristTemperatureModuleInterface {
  /**
   * 获取手腕温度数据样本
   * @param options 查询选项
   * @returns Promise<WristTemperatureSample[]>
   */
  getWristTemperatureSamples(options: WristTemperatureQueryOptions): Promise<WristTemperatureSample[]>
  
  /**
   * 检查手腕温度数据是否可用
   * @returns Promise<WristTemperatureAvailability>
   */
  isWristTemperatureAvailable(): Promise<WristTemperatureAvailability>
}

/**
 * 手腕温度模块
 * 提供Apple Watch Series 8+手腕温度数据访问功能
 */
class WristTemperatureModuleClass implements WristTemperatureModuleInterface {
  private nativeModule: WristTemperatureModuleInterface | null = null

  constructor() {
    // 获取原生模块
    this.nativeModule = NativeModules.WristTemperatureModule
    
    if (!this.nativeModule) {
      console.warn('WristTemperatureModule原生模块未找到，请确保已正确配置Expo插件')
    }
  }

  /**
   * 获取手腕温度数据样本
   */
  async getWristTemperatureSamples(options: WristTemperatureQueryOptions = {}): Promise<WristTemperatureSample[]> {
    if (!this.nativeModule) {
      throw new Error('WristTemperatureModule原生模块不可用')
    }

    try {
      const samples = await this.nativeModule.getWristTemperatureSamples(options)
      return samples
    } catch (error) {
      console.error('获取手腕温度数据失败:', error)
      throw error
    }
  }

  /**
   * 检查手腕温度数据是否可用
   */
  async isWristTemperatureAvailable(): Promise<WristTemperatureAvailability> {
    if (!this.nativeModule) {
      return {
        available: false,
        reason: 'WristTemperatureModule原生模块不可用'
      }
    }

    try {
      const availability = await this.nativeModule.isWristTemperatureAvailable()
      return availability
    } catch (error) {
      console.error('检查手腕温度可用性失败:', error)
      return {
        available: false,
        reason: `检查失败: ${error}`
      }
    }
  }

  /**
   * 获取最近的手腕温度数据
   * @param days 获取最近几天的数据，默认7天
   */
  async getRecentWristTemperature(days: number = 7): Promise<WristTemperatureSample[]> {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    return this.getWristTemperatureSamples({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      ascending: false,
      limit: 100
    })
  }

  /**
   * 获取指定日期范围的手腕温度数据
   */
  async getWristTemperatureInRange(
    startDate: Date,
    endDate: Date,
    limit?: number
  ): Promise<WristTemperatureSample[]> {
    return this.getWristTemperatureSamples({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      ascending: false,
      limit
    })
  }
}

// 导出单例实例
export const WristTemperatureModule = new WristTemperatureModuleClass()

// 默认导出
export default WristTemperatureModule