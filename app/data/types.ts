// 压力趋势数据类型定义

// 指标卡片数据接口
export interface MetricCardData {
  id: string
  title: string
  unit: string
  icon: string
  currentValue: number
  data: {
    time: string
    value: number
    status: "excellent" | "normal" | "attention" | "warning"
  }[]
  statusLabels: {
    excellent: string
    normal: string
    attention: string
    warning: string
  }
}

export interface HrvDataPoint {
  time: string
  value: number | null
  status: "excellent" | "normal" | "attention" | "overload"
}

export interface StressDataPoint {
  time: string
  value: number | null
  status: "excellent" | "normal" | "attention" | "overload"
}

// 历史记录接口
export interface HistoryRecord {
  id: string
  status: "excellent" | "normal" | "attention" | "overload"
  statusText: string
  time: string
  responseTime: number
}

// 指标数据接口
export interface MetricData {
  dailyAverageHrv: {
    value: number
    unit: string
  }
  dailyAverageStress: {
    value: number
    unit: string
  }
  bestStatus: {
    value: number
    unit: string
    time: string
  }
  maxStress: {
    value: number
    unit: string
    time: string
  }
}

export interface coreMetricsData {
  currentValue: number
  eChartData: { time: string; value: number }[]
  normalRange: [number, number]
  avgLast7Days: number
  changeFromLast7Days: number
}

// 睡眠数据接口
export interface TodaySleepSummary {
  totalSleepTime: number
  sleepEfficiency: number
  deepSleepTime: number
  lightSleepTime: number
  remSleepTime: number
  awakeTimes: number
  sleepScore: number
  bedTime: string
  wakeTime: string
}

export interface SleepStage {
  stage: string
  startTime: string
  endTime: string
  duration: number
}

export interface WeekSleepTrendData {
  date: string
  sleepDuration: number
  sleepScore: number
  bedTime: string
  wakeTime: string
}

export interface SleepMetrics {
  avgSleepDuration: coreMetricsData
  avgSleepScore: coreMetricsData
  bestSleep: coreMetricsData
  worstSleep: coreMetricsData
}

export interface SleepVitalSignsItem {
  averageHeartRate: number
  averageRespiratoryRate: number
  averageBloodOxygenSaturation: number
  averageWristTemperature: number
}

export interface SleepData {
  sleepStagesData: SleepStage[]
  weekSleepTrend: WeekSleepTrendData[]
  targetSleepTime: number
  todaySleepTime: number
  sleepEfficiency: number
  sleepHeartRateData: { time: string; value: number }[]
  SleepVitalSignsData: SleepVitalSignsItem
}

export interface FitnessData {
  activityCalories: number
  targetActivityCalories: number
  exerciseMinutes: number
  targetExerciseMinutes: number
  standHours: number
  targetStandHours: number
  weeklyActivityData: { date: string; value: number }[]
  weeklyExerciseData: { date: string; value: number }[]
  weeklyStandingData: { date: string; value: number }[]
}

// 步数数据点类型
export interface StepDataPoint {
  time: string
  value: number
  distance: number // 距离，单位：米
  calories: number // 卡路里，单位：千卡
  floors: number // 楼层
  speed: number // 步行速度，单位：米/分钟
  strideLength: number // 步长，单位：厘米
}

// 数据结构接口
export interface StressDataStructure {
  todayHrvData: HrvDataPoint[]
  todayStressData: StressDataPoint[]
  todayMetrics: MetricData
  historyRecords: HistoryRecord[]
  // 静息心率
  restingHeartRateData: coreMetricsData
  // 睡眠心率
  sleepWeekHeartRateData: coreMetricsData
  // 呼吸率
  breathingRateData: coreMetricsData
  // 手环温度
  wristTemperatureData: coreMetricsData
  // 血氧饱和度
  bloodOxygenData: coreMetricsData
  // 睡眠数据
  sleepData: SleepData
  // 健身数据
  fitnessData: FitnessData
  // 心率数据
  heartRateData: HeartRateData
  // 步数数据
  stepData: {
    targetStep: number
    dayStepData: StepDataPoint[]
  }
}

export interface HeartRateData {
  dayHeartRateData: { time: string; value: number }[]
  weekHeartRateData: { date: string; min: number; max: number }[]
}
