// 压力趋势数据
import type { StressDataStructure } from "./types"

// 整合所有数据
export const mockData: StressDataStructure = {
  // HRV趋势数据
  todayHrvData: [
    { time: "00:00", value: 45, status: "overload" },
    { time: "02:00", value: 65, status: "attention" },
    { time: "04:00", value: 85, status: "normal" },
    { time: "06:30", value: 95, status: "excellent" },
    { time: "08:00", value: 75, status: "normal" },
    { time: "10:00", value: 55, status: "attention" },
    { time: "12:00", value: 50, status: "overload" },
    { time: "14:00", value: 70, status: "normal" },
    { time: "16:00", value: 80, status: "normal" },
    { time: "18:00", value: 90, status: "excellent" },
    { time: "20:00", value: 75, status: "normal" },
    { time: "22:30", value: 60, status: "attention" },
    { time: "23:00", value: 60, status: "attention" },
    { time: "24:00", value: null, status: "attention" },
  ],
  // 压力趋势数据
  todayStressData: [
    { time: "00:17", value: 0, status: "attention" },
    { time: "00:42", value: 0, status: "normal" },
    { time: "01:08", value: 0, status: "normal" },
    { time: "01:33", value: 0, status: "excellent" },
    { time: "01:57", value: 68, status: "excellent" },
    { time: "02:22", value: 0, status: "normal" },
    { time: "02:47", value: 0, status: "attention" },
    { time: "03:12", value: 68, status: "attention" },
    { time: "03:37", value: 0, status: "overload" },
    { time: "04:03", value: 0, status: "overload" },
    { time: "04:28", value: 0, status: "attention" },
    { time: "04:53", value: 0, status: "attention" },
    { time: "05:18", value: 0, status: "normal" },
    { time: "05:43", value: 0, status: "excellent" },
    { time: "06:43", value: 0, status: "excellent" },
    { time: "07:43", value: 0, status: "excellent" },
    { time: "11:58", value: 68, status: "attention" },
    { time: "12:23", value: 50, status: "attention" },
    { time: "12:48", value: 89, status: "overload" },
    { time: "13:13", value: 83, status: "overload" },
    { time: "13:38", value: 74, status: "attention" },
    { time: "14:03", value: 62, status: "attention" },
    { time: "14:28", value: 53, status: "normal" },
    { time: "14:53", value: 44, status: "normal" },
    { time: "15:18", value: 36, status: "excellent" },
    { time: "15:43", value: 41, status: "excellent" },
    { time: "16:08", value: 54, status: "normal" },
    { time: "16:33", value: 66, status: "attention" },
    { time: "16:58", value: 78, status: "attention" },
    { time: "17:23", value: 87, status: "overload" },
    { time: "17:48", value: 81, status: "overload" },
    { time: "18:13", value: 72, status: "attention" },
    { time: "18:38", value: 60, status: "attention" },
    { time: "19:03", value: 51, status: "normal" },
    { time: "19:28", value: 42, status: "excellent" },
    { time: "19:53", value: null, status: "normal" },
  ],
  // 压力今日指标
  todayMetrics: {
    dailyAverageHrv: {
      value: 31,
      unit: "ms",
    },
    dailyAverageStress: {
      value: 52,
      unit: "%",
    },
    bestStatus: {
      value: 76,
      unit: "ms",
      time: "06:12",
    },
    maxStress: {
      value: 18,
      unit: "ms",
      time: "14:31",
    },
  },
  // 压力历史记录
  historyRecords: [
    {
      id: "1",
      status: "overload",
      statusText: "压力过载",
      time: "14:31",
      responseTime: 18,
    },
    {
      id: "2",
      status: "attention",
      statusText: "注意压力",
      time: "13:04",
      responseTime: 26,
    },
    {
      id: "3",
      status: "normal",
      statusText: "状态正常",
      time: "08:12",
      responseTime: 37,
    },
    {
      id: "4",
      status: "excellent",
      statusText: "状态优秀",
      time: "06:12",
      responseTime: 49,
    },
    {
      id: "5",
      status: "attention",
      statusText: "注意压力",
      time: "04:20",
      responseTime: 25,
    },
  ],
  // 静息心率
  restingHeartRateData: {
    currentValue: 45,
    eChartData: [
      { time: "周一", value: 45 },
      { time: "周二", value: 60 },
      { time: "周三", value: 85 },
      { time: "周四", value: 95 },
      { time: "周五", value: 75 },
      { time: "周六", value: 55 },
      { time: "周日", value: 100 },
    ],
    normalRange: [60, 100],
    avgLast7Days: 75,
    changeFromLast7Days: 10,
  },
  // 睡眠心率
  sleepWeekHeartRateData: {
    currentValue: 45,
    eChartData: [
      { time: "周一", value: 45 },
      { time: "周二", value: 65 },
      { time: "周三", value: 85 },
      { time: "周四", value: 95 },
      { time: "周五", value: 75 },
      { time: "周六", value: 55 },
      { time: "周日", value: 50 },
    ],
    normalRange: [60, 100],
    avgLast7Days: 75,
    changeFromLast7Days: 10,
  },
  // 呼吸率
  breathingRateData: {
    currentValue: 68,
    eChartData: [
      { time: "周一", value: 68 },
      { time: "周二", value: 65 },
      { time: "周三", value: 60 },
      { time: "周四", value: 95 },
      { time: "周五", value: 75 },
      { time: "周六", value: 55 },
      { time: "周日", value: 50 },
    ],
    normalRange: [60, 100],
    avgLast7Days: 75,
    changeFromLast7Days: 10,
  },
  // 手腕温度
  wristTemperatureData: {
    currentValue: 36.5,
    eChartData: [
      { time: "周一", value: 36.5 },
      { time: "周二", value: 36.8 },
      { time: "周三", value: 37.2 },
      { time: "周四", value: 37.5 },
      { time: "周五", value: 37.8 },
      { time: "周六", value: 38.2 },
      { time: "周日", value: 38.5 },
    ],
    normalRange: [36, 38],
    avgLast7Days: 75,
    changeFromLast7Days: 10,
  },
  // 血氧饱和度
  bloodOxygenData: {
    currentValue: 98,
    eChartData: [
      { time: "00:00", value: 98 },
      { time: "02:00", value: 97 },
      { time: "04:00", value: 96 },
    ],
    normalRange: [60, 100],
    avgLast7Days: 75,
    changeFromLast7Days: 10,
  },
  // 睡眠数据
  sleepData: {
    targetSleepTime: 460,
    todaySleepTime: 400,
    sleepEfficiency: 92,
    // 睡眠阶段数据
    sleepStagesData: [
      {
        stage: "浅睡",
        endTime: "2025-05-24 02:32:45",
        duration: 51,
        startTime: "2025-05-24 01:41:45",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 02:34:15",
        duration: 1.5,
        startTime: "2025-05-24 02:32:45",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 02:35:45",
        duration: 1.5,
        startTime: "2025-05-24 02:34:15",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 02:37:15",
        duration: 1.5,
        startTime: "2025-05-24 02:35:45",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 02:38:45",
        duration: 1.5,
        startTime: "2025-05-24 02:37:15",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 02:46:15",
        duration: 7.5,
        startTime: "2025-05-24 02:38:45",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 02:59:45",
        duration: 13.5,
        startTime: "2025-05-24 02:46:15",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 03:05:15",
        duration: 5.5,
        startTime: "2025-05-24 02:59:45",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 03:32:45",
        duration: 27.5,
        startTime: "2025-05-24 03:05:15",
      },
      {
        stage: "深睡",
        endTime: "2025-05-24 03:48:45",
        duration: 16,
        startTime: "2025-05-24 03:32:45",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 04:14:15",
        duration: 25.5,
        startTime: "2025-05-24 03:48:45",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 04:43:45",
        duration: 29.5,
        startTime: "2025-05-24 04:14:15",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 04:44:45",
        duration: 1,
        startTime: "2025-05-24 04:43:45",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 04:58:15",
        duration: 13.5,
        startTime: "2025-05-24 04:44:45",
      },
      {
        stage: "清醒",
        endTime: "20 25-05-24 05:03:15",
        duration: 5,
        startTime: "2025-05-24 04:58:15",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 05:58:45",
        duration: 55.5,
        startTime: "2025-05-24 05:03:15",
      },
      {
        stage: "深睡",
        endTime: "2025-05-24 06:09:45",
        duration: 11,
        startTime: "2025-05-24 05:58:45",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 06:12:15",
        duration: 2.5,
        startTime: "2025-05-24 06:09:45",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 06:52:15",
        duration: 40,
        startTime: "2025-05-24 06:12:15",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 07:42:15",
        duration: 50,
        startTime: "2025-05-24 06:52:15",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 07:50:15",
        duration: 8,
        startTime: "2025-05-24 07:42:15",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 07:51:15",
        duration: 1,
        startTime: "2025-05-24 07:50:15",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 08:08:15",
        duration: 17,
        startTime: "2025-05-24 07:51:15",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 08:08:45",
        duration: 0.5,
        startTime: "2025-05-24 08:08:15",
      },
      {
        stage: "REM",
        endTime: "2025-05-24 08:12:15",
        duration: 3.5,
        startTime: "2025-05-24 08:08:45",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 08:40:45",
        duration: 28.5,
        startTime: "2025-05-24 08:12:15",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 08:45:15",
        duration: 4.5,
        startTime: "2025-05-24 08:40:45",
      },
      {
        stage: "清醒",
        endTime: "2025-05-24 08:50:45",
        duration: 5.5,
        startTime: "2025-05-24 08:45:15",
      },
      {
        stage: "浅睡",
        endTime: "2025-05-24 08:57:45",
        duration: 7,
        startTime: "2025-05-24 08:50:45",
      },
    ],
    weekSleepTrend: [
      {
        date: "2025-05-18",
        sleepDuration: 420,
        sleepScore: 80,
        bedTime: "23:00",
        wakeTime: "06:00",
      },
      {
        date: "2025-05-19",
        sleepDuration: 450,
        sleepScore: 85,
        bedTime: "22:30",
        wakeTime: "06:00",
      },
      {
        date: "2025-05-20",
        sleepDuration: 480,
        sleepScore: 90,
        bedTime: "22:00",
        wakeTime: "06:00",
      },
      {
        date: "2025-05-21",
        sleepDuration: 460,
        sleepScore: 88,
        bedTime: "22:30",
        wakeTime: "06:10",
      },
      {
        date: "2025-05-22",
        sleepDuration: 470,
        sleepScore: 89,
        bedTime: "22:15",
        wakeTime: "06:05",
      },
      {
        date: "2025-05-23",
        sleepDuration: 440,
        sleepScore: 82,
        bedTime: "23:15",
        wakeTime: "06:15",
      },
      {
        date: "2025-05-24",
        sleepDuration: 460,
        sleepScore: 85,
        bedTime: "23:00",
        wakeTime: "07:00",
      },
    ],
    sleepHeartRateData: [
      { time: "2023-01-01 23:00:00", value: 60 },
      { time: "2023-01-01 23:23:00", value: 60 },
      { time: "2023-01-01 23:30:00", value: 65 },
      { time: "2023-01-01 23:35:00", value: 50 },
      { time: "2023-01-01 23:40:00", value: 60 },
      { time: "2023-01-02 00:00:00", value: 60 },
      { time: "2023-01-02 00:10:00", value: 68 },
      { time: "2023-01-02 00:15:00", value: 55 },
      { time: "2023-01-02 00:20:00", value: 62 },
      { time: "2023-01-02 00:30:00", value: 60 },
      { time: "2023-01-02 01:00:00", value: 60 },
      { time: "2023-01-02 01:10:00", value: 63 },
      { time: "2023-01-02 01:15:00", value: 58 },
      { time: "2023-01-02 01:20:00", value: 60 },
      { time: "2023-01-02 02:00:00", value: 60 },
      { time: "2023-01-02 02:10:00", value: 65 },
      { time: "2023-01-02 02:15:00", value: 52 },
      { time: "2023-01-02 02:20:00", value: 60 },
      { time: "2023-01-02 03:00:00", value: 60 },
      { time: "2023-01-02 03:16:00", value: 60 },
      { time: "2023-01-02 03:20:00", value: 68 },
      { time: "2023-01-02 03:25:00", value: 45 },
      { time: "2023-01-02 03:30:00", value: 60 },
      { time: "2023-01-02 04:00:00", value: 60 },
      { time: "2023-01-02 04:10:00", value: 65 },
      { time: "2023-01-02 04:15:00", value: 50 },
      { time: "2023-01-02 04:20:00", value: 60 },
      { time: "2023-01-02 05:00:00", value: 60 },
      { time: "2023-01-02 05:10:00", value: 63 },
      { time: "2023-01-02 05:15:00", value: 58 },
      { time: "2023-01-02 05:20:00", value: 60 },
      { time: "2023-01-02 06:00:00", value: 60 },
      { time: "2023-01-02 06:10:00", value: 65 },
      { time: "2023-01-02 06:15:00", value: 50 },
      { time: "2023-01-02 06:20:00", value: 60 },
      { time: "2023-01-02 07:04:00", value: 60 },
      { time: "2023-01-02 07:10:00", value: 60 },
    ],
    SleepVitalSignsData: {
      averageHeartRate: 55, // 平均心率
      averageRespiratoryRate: 19, // 平均呼吸频率
      averageBloodOxygenSaturation: 110, // 平均血氧饱和度
      averageWristTemperature: 38.5, // 平均手腕温度
    },
  },
  // 健身数据
  fitnessData: {
    activityCalories: 270,
    targetActivityCalories: 400,
    exerciseMinutes: 16,
    targetExerciseMinutes: 30,
    standHours: 8,
    targetStandHours: 12,
    weeklyActivityData: [
      { date: "2024-06-15 00:00:00", value: 520 },
      { date: "2024-06-16 00:00:00", value: 380 },
      { date: "2024-06-17 00:00:00", value: 580 },
      { date: "2024-06-18 00:00:00", value: 280 },
      { date: "2024-06-19 00:00:00", value: 420 },
      { date: "2024-06-20 00:00:00", value: 0 },
      { date: "2024-06-21 00:00:00", value: 0 },
    ],
    weeklyExerciseData: [
      { date: "2024-06-15 00:00:00", value: 45 },
      { date: "2024-06-16 00:00:00", value: 60 },
      { date: "2024-06-17 00:00:00", value: 30 },
      { date: "2024-06-18 00:00:00", value: 75 },
      { date: "2024-06-19 00:00:00", value: 90 },
      { date: "2024-06-20 00:00:00", value: 0 },
      { date: "2024-06-21 00:00:00", value: 120 },
    ],
    weeklyStandingData: [
      { date: "2024-06-15 00:00:00", value: 8 },
      { date: "2024-06-16 00:00:00", value: 10 },
      { date: "2024-06-17 00:00:00", value: 9 },
      { date: "2024-06-18 00:00:00", value: 11 },
      { date: "2024-06-19 00:00:00", value: 12 },
      { date: "2024-06-20 00:00:00", value: 6 },
      { date: "2024-06-21 00:00:00", value: 7 },
    ],
  },
  // 心率数据
  heartRateData: {
    dayHeartRateData: [
      { time: "2024-06-21 00:00:00", value: 60 },
      { time: "2024-06-21 00:05:00", value: 62 },
      { time: "2024-06-21 00:10:00", value: 65 },
      { time: "2024-06-21 00:15:00", value: 60 },
      { time: "2024-06-21 00:20:00", value: 68 },
      { time: "2024-06-21 00:25:00", value: 60 },
      { time: "2024-06-21 00:30:00", value: 62 },
      { time: "2024-06-21 00:35:00", value: 65 },
      { time: "2024-06-21 00:40:00", value: 60 },
      { time: "2024-06-21 00:45:00", value: 68 },
      { time: "2024-06-21 00:50:00", value: 60 },
      { time: "2024-06-21 00:55:00", value: 62 },
      { time: "2024-06-21 01:00:00", value: 65 },
      { time: "2024-06-21 01:05:00", value: 60 },
      { time: "2024-06-21 01:10:00", value: 68 },
      { time: "2024-06-21 01:15:00", value: 60 },
      { time: "2024-06-21 01:20:00", value: 62 },
      { time: "2024-06-21 01:25:00", value: 65 },
      { time: "2024-06-21 01:30:00", value: 60 },
      { time: "2024-06-21 01:35:00", value: 68 },
    ],
    weekHeartRateData: [
      { date: "2024-06-15", min: 80, max: 120 },
      { date: "2024-06-16", min: 75, max: 115 },
      { date: "2024-06-17", min: 82, max: 122 },
      { date: "2024-06-18", min: 78, max: 118 },
      { date: "2024-06-19", min: 85, max: 125 },
      { date: "2024-06-20", min: 77, max: 117 },
      { date: "2024-06-21", min: 88, max: 128 },
    ],
  },
  // 步数数据
  stepData: {
    targetStep: 6000,
    dayStepData: [
      {
        time: "2025-06-28",
        value: 0,
        distance: 800,
        calories: 30,
        floors: 1,
        speed: 70,
        strideLength: 60,
      },
      {
        time: "2025-06-28",
        value: 1023,
        distance: 800,
        calories: 30,
        floors: 1,
        speed: 70,
        strideLength: 60,
      },
      {
        time: "2025-06-29",
        value: 5200,
        distance: 4000,
        calories: 150,
        floors: 4,
        speed: 80,
        strideLength: 62,
      },
      {
        time: "2025-06-30",
        value: 7000,
        distance: 6200,
        calories: 220,
        floors: 6,
        speed: 85,
        strideLength: 63,
      },
      {
        time: "2025-07-01",
        value: 12055,
        distance: 9500,
        calories: 350,
        floors: 10,
        speed: 90,
        strideLength: 65,
      },
      {
        time: "2025-07-02",
        value: 1500,
        distance: 1200,
        calories: 45,
        floors: 2,
        speed: 75,
        strideLength: 61,
      },
      {
        time: "2025-07-03",
        value: 7000,
        distance: 800,
        calories: 30,
        floors: 1,
        speed: 70,
        strideLength: 60,
      },
      {
        time: "2025-07-04",
        value: 6001,
        distance: 5000,
        calories: 200,
        floors: 10,
        speed: 90,
        strideLength: 65,
      },
      {
        time: "2025-07-05",
        value: 5001,
        distance: 5000,
        calories: 200,
        floors: 10,
        speed: 90,
        strideLength: 65,
      },
    ],
  },
}
