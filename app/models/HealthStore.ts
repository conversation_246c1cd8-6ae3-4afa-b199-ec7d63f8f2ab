import { types } from "mobx-state-tree"
import { HealthValue } from "react-native-health"
import { formatDate, formatDateToYyyyMmDd } from "../utils/formatDate"
import { IconTypes } from "../components/Icon"
// 定义每日心率数据点接口
export interface DailyHeartRate {
  value: number
  date: string // YYYY-MM-DD 格式
  count: number // 数据点数量
}

export interface RestingHeartRateData {
  value: number
  date: string // YYYY-MM-DD 格式
  count: number // 数据点数量
}

export interface HRVDataPoint extends HealthValue {
  value: number
  startDate: string
  endDate: string
}

// 定义每日RMSSD数据点接口
export interface DailyRMSSDData {
  date: string // YYYY-MM-DD 格式
  value: number // 当天的平均RMSSD值
}

// 获取原始逐搏数据
type HeartbeatSample = {
  startDate: string // 开始时间
  endDate: string // 结束时间
  id?: string // 可选ID
  heartbeatSeries?: any // 元数据
}

/**
 * 计算 HRV-RMSSD 值
 * calculateRRIntervals 将 逐博测量换算成心跳间隔
 * RMSSD = √(1/(n-1) * Σ[(RR(i+1) - RR(i))^2])
 *
 * 具体步骤：
 * 逐博测量 = [6.8710937,7.64453125,8.4453125,...]
 * Δ差值 = [7.64453125-6.8710937, 8.4453125-7.64453125, ...]
 * 心跳间隔 （秒换算成毫秒）（假数据）
 * RR=[810,800,790,810,805,790,795,805, ...]
 * 计算差值
 * ΔRR=[810−800,790−810,805−790,795−805, ...]
 * 差值的平方
 * ΔRR^2 = [10^2,(−20)^2,15^2,(−10)^2, ....]
 * 差值的平方 ∑ 求和
 * ∑ΔRR^2 = 10^2 + (−20)^2 + 15^2 + (−10)^2, ...
 * 平方和  求均值
 * 1/n * ∑ΔRR^2 = 1/n * (10^2+(−20)^2 +15^2+(−10)^2 ...)
 * 开根号
 * √(1/n * ∑ΔRR^2) = √(1/n * (10^2+(−20)^2 +15^2+(−10)^2 ...))
 * @param heartRateSamples 心率数据数组
 * @returns 计算得到的 RMSSD 值，如果数据不足则返回 null
 */
const calculateRMSSD = (
  heartRateSamples: [{ precededByGap: boolean; timeSinceSeriesStart: number }],
): number | null => {
  // 1. 检查数据是否足够（至少需要2个数据点）
  if (heartRateSamples.length < 2) {
    console.warn("至少需要2个心率数据点来计算RMSSD")
    return 0
  }
  try {
    // 2. 计算RR间期（毫秒）
    const rrIntervals = calculateRRIntervals(heartRateSamples)

    // 3. 计算相邻RR间期的差值平方和
    let sumOfSquaredDifferences = 0
    for (let i = 0; i < rrIntervals.length - 1; i++) {
      const diff = rrIntervals[i + 1] - rrIntervals[i]
      sumOfSquaredDifferences += diff * diff
    }

    // 4. 计算平均值并开平方根
    const meanSquaredDifference = sumOfSquaredDifferences / (rrIntervals.length - 1)
    const rmssd = Math.sqrt(meanSquaredDifference)

    // 直接取整，不进行四舍五入
    return Math.trunc(rmssd)
  } catch (error) {
    console.error("计算RMSSD时出错:", error)
    return null
  }
}

function calculateRRIntervals(
  heartbeatSeries: [{ precededByGap: boolean; timeSinceSeriesStart: number }],
) {
  // 从第二个心跳开始遍历
  const validIntervalsMs = [] // 存放有效间隔，单位：毫秒
  for (let i = 1; i < heartbeatSeries.length; i++) {
    const currentBeat = heartbeatSeries[i]
    const previousBeat = heartbeatSeries[i - 1]

    // 只有当 precededByGap 为 false 时，间隔才有效
    if (currentBeat.precededByGap === false) {
      // 计算时间差（秒）
      const intervalInSeconds = currentBeat.timeSinceSeriesStart - previousBeat.timeSinceSeriesStart

      // 转换为毫秒并存入数组
      const intervalInMs = Math.round(intervalInSeconds * 1000)
      validIntervalsMs.push(intervalInMs)
    }
  }

  return validIntervalsMs
}

export const HealthStore = types
  .model("HealthStore", {
    // 用户基本信息
    userInfo: types.optional(
      types.model({
        gender: types.maybeNull(types.string), // 'male' 或 'female'
        age: types.maybeNull(types.number), // 年龄
        height: types.maybeNull(types.number), // 身高(cm)
        weight: types.maybeNull(types.number), // 体重(kg)
      }),
      { gender: null, age: null, height: null, weight: null },
    ),
    stepCount: types.maybeNull(types.number),
    heartRateDay: types.array(
      types.model({
        value: types.number,
        time: types.string, // YYYY-MM-DD HH:mm:ss 格式
      }),
    ),
    heartRateLast30Days: types.array(
      types.model({
        value: types.number,
        date: types.string, // YYYY-MM-DD 格式
        count: types.number, // 数据点数量
      }),
    ),
    
    restingHeartRateLast30Days: types.array(
      types.model({
        value: types.number,
        date: types.string, // YYYY-MM-DD 格式
        count: types.number, // 数据点数量
      }),
    ),
    rmssdHRVToday: types.array(types.frozen<HRVDataPoint>()), // 当天HRV数据
    dailyRMSSDData: types.array(types.frozen<DailyRMSSDData>()), // 过去30天每日RMSSD数据
    averageRMSSD: types.maybeNull(types.number), // 过去30天平均RMSSD
    heartRateDuringSleep: types.array(types.frozen<HealthValue>()), // 睡眠期间心率
    respiratoryRate: types.array(
      types.model({
        value: types.number,
        date: types.string, // YYYY-MM-DD 格式
        count: types.number, // 数据点数量
      }),
    ), // 呼吸率
    bloodOxygen: types.array(
      types.model({
        value: types.number,
        date: types.string, // YYYY-MM-DD 格式
        count: types.number, // 数据点数量
      }),
    ), // 血氧饱和度
    wristTemperature: types.array(
      types.model({
        value: types.number,
        date: types.string, // YYYY-MM-DD 格式
        count: types.number, // 数据点数量
      }),
    ), // 手腕温度
    isLoading: false,
    error: types.maybeNull(types.string),
    permissionsGranted: false,
    metricsData: types.optional(
      types.array(
        types.frozen<{
          currentValue: number
          eChartData: { time: string; value: number | null }[]
          title: string
          unit: string
          icon: IconTypes
          eChartLineColor: string
          normalRange: [number, number]
          avgLast7Days: number
          changeFromLast7Days: number
          /** 指标定义说明文字 */
          metricDefinition?: string
          /** 指标说明标题 */
          metricTitle?: string
          id?: string
        }>(),
      ),
      [],
    ),
  })
  .actions((self) => ({
    setLoading(loading: boolean) {
      self.isLoading = loading
    },
    setError(error: string | null) {
      self.error = error
    },
    setPermissionsGranted(granted: boolean) {
      self.permissionsGranted = granted
    },
    setStepCount(count: number) {
      self.stepCount = count
    },
    // 设置当天心率数据
    setHeartRateDay(samples: HealthValue[]) {
      const data = samples.map((sample) => ({
        value: Math.floor(sample.value),
        time: formatDate(sample.startDate, "yyyy-MM-dd HH:mm:ss"),
        // endDate: formatDate(sample.endDate, "yyyy-MM-dd HH:mm:ss"),
      }))
      console.log("🚀 ~ 设置当天心率数据data ~ data:", data.length + "条")
      self.heartRateDay.replace(data)
    },
    /**
     * 设置30天心率数据
     * @param samples 原始心率数据数组
     */
    setHeartRateLast30Days(samples: HealthValue[]) {
      try {
        // 1. 按天分组并计算平均值
        const dailyAverages = samples.reduce<
          Record<string, { sum: number; count: number; date: string }>
        >((acc, sample) => {
          const dateKey = formatDateToYyyyMmDd(sample.startDate) // YYYY-MM-DD

          if (!acc[dateKey]) {
            acc[dateKey] = {
              sum: 0,
              count: 0,
              date: dateKey, // 只保存YYYY-MM-DD格式的日期
            }
          }

          acc[dateKey].sum += sample.value
          acc[dateKey].count++

          return acc
        }, {})

        // 2. 转换为最终格式
        const formattedData = Object.values(dailyAverages)
          .map(({ sum, count, date }) => ({
            value: Math.round((sum / count) * 10) / 10, // 保留一位小数
            date, // YYYY-MM-DD格式的日期
            count, // 可选：记录每天的数据点数量
          }))
          // 3. 按日期排序
          .sort((a, b) => a.date.localeCompare(b.date))

        console.log(
          `🚀 ~ 处理完成 heartRateLast30Days：共 ${samples.length} 条数据聚合为 ${formattedData.length} 天平均值`,
        )
        console.log("🚀 ~ 30天心率数据data ~ data:", formattedData)

        // 4. 更新 store
        self.heartRateLast30Days.replace(formattedData)
      } catch (error) {
        console.error("处理30天心率数据时出错:", error)
        throw error
      }
    },

    

    // 设置30天静息心率数据
    setRestingHeartRateLast30Days(samples: HealthValue[]) {
      try {
        // 2. 处理数据：value 取整，date 格式化为 YYYY-MM-DD
        const processedData = samples.map((sample) => ({
          value: Math.floor(sample.value), // 取整
          date: formatDate(sample.startDate, "yyyy-MM-dd"), // 使用 formatDate 处理时区
          count: 1,
        }))

        // 3. 按日期排序
        processedData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        console.log("🚀 ~ 30天静息心率- restingHeartRateLast30Days:", processedData.length + "条")

        // 4. 更新 store
        self.restingHeartRateLast30Days.replace(processedData)
      } catch (error) {
        console.error("处理30天静息心率数据时出错:", error)
        throw error
      }
    },

    // 设置当天HRV数据
    setRMSSDHRVToday(samples: HeartbeatSample[]) {
      const data: HRVDataPoint[] = []
      samples.forEach((item) => {
        if (item.heartbeatSeries && item.heartbeatSeries.length > 0) {
          const rmssd = calculateRMSSD(item.heartbeatSeries)
          if (rmssd !== null) {
            data.push({
              value: rmssd,
              startDate: formatDate(item.startDate, "yyyy-MM-dd HH:mm:ss"),
              endDate: formatDate(item.endDate, "yyyy-MM-dd HH:mm:ss"),
            })
          }
        }
      })
      console.log("🚀 ~ 当天HRV数据 ~ rmssdHRVToday:", data)
      self.rmssdHRVToday.replace(data)
    },

    /**
     * 设置过去30天每日RMSSD数据和平均RMSSD
     * @param samples 原始逐搏数据数组
     */
    setDailyAndAverageRMSSD(samples: HeartbeatSample[]) {
      const dailyRMSSDMap = new Map<string, number[]>()

      samples.forEach((sample) => {
        if (sample.heartbeatSeries && sample.heartbeatSeries.length > 0) {
          const rmssd = calculateRMSSD(sample.heartbeatSeries)
          if (rmssd !== null) {
            const dateKey = formatDateToYyyyMmDd(sample.startDate)
            if (!dailyRMSSDMap.has(dateKey)) {
              dailyRMSSDMap.set(dateKey, [])
            }
            dailyRMSSDMap.get(dateKey)?.push(rmssd)
          }
        }
      })

      // console.log("🚀 ~ setDailyAndAverageRMSSD ~ 30日RMSSD数据:", dailyRMSSDMap)
      const dailyRMSSDData: DailyRMSSDData[] = []
      let totalRMSSD = 0
      let totalDaysWithData = 0

      // 计算每日平均RMSSD
      dailyRMSSDMap.forEach((rmssdValues, dateKey) => {
        const dailyAverage = rmssdValues.reduce((sum, val) => sum + val, 0) / rmssdValues.length
        dailyRMSSDData.push({
          date: dateKey,
          value: Math.trunc(dailyAverage),
        })
        totalRMSSD += dailyAverage
        totalDaysWithData++
      })

      // 按日期排序
      dailyRMSSDData.sort((a, b) => a.date.localeCompare(b.date))
      // console.log("🚀 ~ setDailyAndAverageRMSSD ~ 30日平均RMSSD数据:", dailyRMSSDData)

      // 计算30天平均RMSSD
      if (totalDaysWithData > 0) {
        self.averageRMSSD = Math.round(totalRMSSD / totalDaysWithData)
        console.log("🚀 ~ RMSSD基线-averageRMSSD:", self.averageRMSSD)
      } else {
        self.averageRMSSD = null
      }
    },

    // 设置睡眠期间心率数据
    setHeartRateDuringSleep(samples: HealthValue[]) {
      const data = samples.map((sample) => ({
        value: Math.floor(sample.value),
        startDate: formatDate(sample.startDate, "yyyy-MM-dd HH:mm:ss"),
        endDate: formatDate(sample.endDate, "yyyy-MM-dd HH:mm:ss"),
      }))
      // console.log("🚀 ~ setHeartRateDuringSleep-设置睡眠期间心率数据data ~ data:", data)
      self.heartRateDuringSleep.replace(data)
    },

    /**
     * 设置呼吸率的日均数据
     * @param samples 原始呼吸率数据数组
     */
    setRespiratoryRate(samples: HealthValue[]) {
      if (!samples || samples.length === 0) {
        self.respiratoryRate.clear()
        return
      }

      const dailyAverages = samples.reduce<
        Record<string, { sum: number; count: number; date: string }>
      >((acc, sample) => {
        const dateKey = formatDateToYyyyMmDd(sample.startDate)
        if (!acc[dateKey]) {
          acc[dateKey] = { sum: 0, count: 0, date: dateKey }
        }
        acc[dateKey].sum += sample.value
        acc[dateKey].count++
        return acc
      }, {})

      const formattedData = Object.values(dailyAverages)
        .map(({ sum, count, date }) => ({
          value: Math.round(sum / count),
          date,
          count,
        }))
        .sort((a, b) => a.date.localeCompare(b.date))

      self.respiratoryRate.replace(formattedData)
    },

    /**
     * 设置血氧饱和度的日均数据
     * @param samples 原始血氧数据数组
     */
    setBloodOxygen(samples: HealthValue[]) {
      if (!samples || samples.length === 0) {
        self.bloodOxygen.clear()
        return
      }

      const dailyAverages = samples.reduce<
        Record<string, { sum: number; count: number; date: string }>
      >((acc, sample) => {
        const dateKey = formatDateToYyyyMmDd(sample.startDate)
        if (!acc[dateKey]) {
          acc[dateKey] = { sum: 0, count: 0, date: dateKey }
        }
        acc[dateKey].sum += sample.value
        acc[dateKey].count++
        return acc
      }, {})

      const formattedData = Object.values(dailyAverages)
        .map(({ sum, count, date }) => ({
          value: parseFloat(((sum / count) * 100).toFixed(1)), // 血氧值通常是百分比，保留一位小数
          date,
          count,
        }))
        .sort((a, b) => a.date.localeCompare(b.date))

      self.bloodOxygen.replace(formattedData)
    },

    /**
     * 设置手腕温度的日均数据
     * @param samples 原始手腕温度数据数组
     */
    setWristTemperature(samples: HealthValue[]) {
      if (!samples || samples.length === 0) {
        self.wristTemperature.clear()
        return
      }

      const dailyAverages = samples.reduce<
        Record<string, { sum: number; count: number; date: string }>
      >((acc, sample) => {
        const dateKey = formatDateToYyyyMmDd(sample.startDate)
        if (!acc[dateKey]) {
          acc[dateKey] = { sum: 0, count: 0, date: dateKey }
        }
        acc[dateKey].sum += sample.value
        acc[dateKey].count++
        return acc
      }, {})

      const formattedData = Object.values(dailyAverages)
        .map(({ sum, count, date }) => ({
          value: parseFloat((sum / count).toFixed(1)), // 保留一位小数
          date,
          count,
        }))
        .sort((a, b) => a.date.localeCompare(b.date))
      console.log("🚀 ~ setWristTemperature ~ 设置手腕温度的日均数据:", formattedData)

      self.wristTemperature.replace(formattedData)
    },

    // 设置用户基本信息
    setUserInfo(userInfo: {
      gender: string | null
      age: number | null
      height: number | null
      weight: number | null
    }) {
      self.userInfo = userInfo
    },

    reset() {
      // 重置用户信息
      self.userInfo = { gender: null, age: null, height: null, weight: null }
      self.stepCount = null
      self.heartRateDay.clear()
      self.heartRateLast30Days.clear()
      self.restingHeartRateLast30Days.clear()
      self.rmssdHRVToday.clear()
      self.averageRMSSD = null
      self.bloodOxygen.clear()
      self.wristTemperature.clear()
      self.respiratoryRate.clear()
      self.heartRateDuringSleep.clear()
      self.metricsData.clear()
      self.isLoading = false
      self.error = null
      self.permissionsGranted = false
    },

    setMetricsData(
      data: Array<{
        currentValue: number
        eChartData: { time: string; value: number | null }[]
        title: string
        unit: string
        icon: IconTypes
        eChartLineColor: string
        normalRange: [number, number]
        avgLast7Days: number
        changeFromLast7Days: number
        /** 指标定义说明文字 */
        metricDefinition?: string
        /** 指标说明标题 */
        metricTitle?: string
        id?: string
      }>,
    ) {
      self.metricsData.replace(data)
    },
  }))

export type HealthStoreType = typeof HealthStore.Type
