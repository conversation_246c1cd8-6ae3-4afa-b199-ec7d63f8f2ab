import { Instance, SnapshotOut, types } from "mobx-state-tree"
import * as AppleAuthentication from "expo-apple-authentication"
import * as SecureStore from "expo-secure-store"

export const AuthStoreModel = types
  .model("AuthStore")
  .props({
    isAuthenticated: types.optional(types.boolean, false),
    user: types.maybe(types.frozen<AppleAuthentication.AppleAuthenticationCredential>()),
  })
  .actions((self) => ({
    setAuthenticated(value: boolean) {
      self.isAuthenticated = value
    },
    setUser(user: AppleAuthentication.AppleAuthenticationCredential | null) {
      self.user = user || undefined
      self.isAuthenticated = !!user
      
      // 保存用户信息到安全存储
      if (user) {
        SecureStore.setItemAsync("user", JSON.stringify(user))
      } else {
        SecureStore.deleteItemAsync("user")
      }
    },
    async checkAuthStatus() {
      try {
        // 检查是否有保存的用户信息
        const userJson = await SecureStore.getItemAsync("user")
        if (userJson) {
          const user = JSON.parse(userJson)
          this.setUser(user)
          return true
        }
        return false
      } catch (error) {
        console.error("检查认证状态失败:", error)
        return false
      }
    },
    async signInWithApple() {
      try {
        const credential = await AppleAuthentication.signInAsync({
          requestedScopes: [
            AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
            AppleAuthentication.AppleAuthenticationScope.EMAIL,
          ],
        })

        // 登录成功
        this.setUser(credential)
        return { success: true, user: credential }
      } catch (e: any) {
        if (e.code === "ERR_CANCELED") {
          // 用户取消了登录
          return { success: false, error: "用户取消了登录" }
        } else {
          // console.error("Apple 登录错误:", e)
          return { success: false, error: e.message || "登录失败" }
        }
      }
    },
    async signOut() {
      this.setUser(null)
    },
  }))

export interface AuthStore extends Instance<typeof AuthStoreModel> {}
export interface AuthStoreSnapshot extends SnapshotOut<typeof AuthStoreModel> {}

// 辅助函数：创建 AuthStore 实例
export const createAuthStore = () => AuthStoreModel.create({})
