import { useEffect, useState } from "react"
import { RootStore, RootStoreModel } from "../RootStore"
import { setupRootStore } from "./setupRootStore"

/**
 * Hook to initialize and manage the root store.
 * This hook handles the initialization of the root store and manages its state.
 */
export function useInitialRootStore() {
  const [rootStore, setRootStore] = useState<RootStore | null>(null)
  const [rehydrated, setRehydrated] = useState(false)

  useEffect(() => {
    let unsubscribe: (() => void) | undefined

    async function setup() {
      const rootStoreInstance = RootStoreModel.create({})
      const { rootStore: store, unsubscribe: unsub } = await setupRootStore(rootStoreInstance)
      setRootStore(store)
      unsubscribe = unsub
      setRehydrated(true)
    }

    setup()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [])

  return { rootStore, rehydrated }
}
