import { BottomTabScreenProps, createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { CompositeScreenProps } from "@react-navigation/native"
import { TextStyle, ViewStyle } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Icon } from "../components"
import { HomeScreen, ActiveScreen, SettingScreen } from "../screens"
import type { ThemedStyle } from "@/theme"
import { AppStackParamList, AppStackScreenProps } from "./AppNavigator"
import { useAppTheme } from "@/utils/useAppTheme"

export type TabParamList = {
  HomeScreen: undefined
  ActiveScreen: undefined
  SettingScreen: undefined
}
export type DemoTabParamList = {
  DemoDebug: undefined
}

/**
 * Helper for automatically generating navigation prop types for each route.
 *
 * More info: https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type TabScreenProps<T extends keyof TabParamList> = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, T>,
  AppStackScreenProps<keyof AppStackParamList>
>

const Tab = createBottomTabNavigator<TabParamList>()

/**
 * This is the main navigator for the demo screens with a bottom tab bar.
 * Each tab is a stack navigator with its own set of screens.
 *
 * More info: https://reactnavigation.org/docs/bottom-tab-navigator/
 * @returns {JSX.Element} The rendered `DemoNavigator`.
 */
export function StressNavigator() {
  const { bottom } = useSafeAreaInsets()
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
        tabBarStyle: themed([$tabBar, { height: bottom + 70 }]),
        tabBarActiveTintColor: colors.tint,
        tabBarInactiveTintColor: colors.tintInactive,
        tabBarLabelStyle: themed($tabBarLabel),
        tabBarItemStyle: themed($tabBarItem),
      }}
    >
      <Tab.Screen
        name="HomeScreen"
        component={HomeScreen}
        options={{
          tabBarLabel: '今天',
          tabBarIcon: ({ focused }) => (
            <Icon
              icon="tabbarHome"
              color={focused ? colors.tint : colors.tintInactive}
              size={28}
              useOriginalColor={!focused}
            />
          ),
        }}
      />
      <Tab.Screen
        name="ActiveScreen"
        component={ActiveScreen}
        options={{
          tabBarLabel: '活动',
          tabBarIcon: ({ focused }) => (
            <Icon
              icon="tabbarActivity"
              color={focused ? colors.tint : colors.tintInactive}
              size={28}
              useOriginalColor={!focused}
            />
          ),
        }}
      />
      <Tab.Screen
        name="SettingScreen"
        component={SettingScreen}
        options={{
          tabBarLabel: '设置',
          tabBarIcon: ({ focused }) => (
            <Icon
              icon="tabbarSetting"
              color={focused ? colors.tint : colors.tintInactive}
              size={28}
              useOriginalColor={!focused}
            />
          ),
        }}
      />
      {/* <Tab.Screen
        name="setting"
        component={DemoDebugScreen}
        options={{
          tabBarLabel: translate("demoNavigator:debugTab"),
          tabBarIcon: ({ focused }) => (
            <Icon icon="bell" color={focused ? colors.tint : colors.tintInactive} size={28} />
          ),
        }}
      /> */}
    </Tab.Navigator>
  )
}

const $tabBar: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
  borderTopColor: colors.transparent,
})

const $tabBarItem: ThemedStyle<ViewStyle> = () => ({
  paddingTop: 0,
})

const $tabBarLabel: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 10,
  fontFamily: typography.primary.medium,
  lineHeight: 12,
  // 这里不需要设置 color，因为颜色会由 tabBarActiveTintColor 和 tabBarInactiveTintColor 控制
})
