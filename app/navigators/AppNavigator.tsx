/**
 * 应用导航器（以前称为"AppNavigator"和"MainNavigator"）用于应用的主要导航流程。
 * 一般来说，它将包含一个认证流程（注册、登录、忘记密码）
 * 和一个用户登录后使用的"主要"流程。
 */
import { NavigationContainer, NavigatorScreenParams } from "@react-navigation/native"
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import { observer } from "mobx-react-lite"
import * as Screens from "@/screens"
import Config from "../config"
import { StressNavigator, DemoTabParamList } from "./StressNavigator"
import { navigationRef, useBackButtonHandler } from "./navigationUtilities"
import { useAppTheme, useThemeProvider } from "@/utils/useAppTheme"
import { ComponentProps } from "react"
import { useStores } from "@/models"

/**
 * 此类型允许TypeScript知道在此导航器中定义了哪些路由
 * 以及在导航到这些路由时它们可能采用的属性（如果有的话）。
 *
 * 如果不允许参数，请传递`undefined`。一般来说，我们
 * 建议使用您的MobX-State-Tree存储来保持应用状态
 * 而不是通过导航参数传递状态。
 *
 * 有关更多信息，请参阅此文档：
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Welcome: undefined
  Demo: NavigatorScreenParams<DemoTabParamList>
  StressDayScreen: undefined
  MetricsDetailScreen: { metricId: string }
  SleepDetailScreen: undefined
  FitnessDetailScreen: undefined
  HeartRateDetailScreen: undefined
  StepCountDetailScreen: undefined
  StressNavigator: undefined
  ActiveScreen: undefined
  ProfileScreen: undefined
  LoginScreen: undefined // 添加登录页面路由
  DialScreen: undefined // 表盘页面
  // 🔥 您的屏幕在这里
  // IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

/**
 * 这是所有路由名称的列表，如果在该屏幕中按下返回按钮，将退出应用。
 * 仅影响Android。
 */
const exitRoutes = Config.exitRoutes

/**
 * AppStackScreenProps 类型定义
 * 
 * 这是一个泛型类型，用于为应用程序堆栈导航器中的屏幕组件提供类型安全的 props
 * 
 * @template T - 必须是 AppStackParamList 中定义的路由名称之一
 * 
 * 功能说明：
 * 1. 继承自 React Navigation 的 NativeStackScreenProps
 * 2. 提供导航相关的 props，包括 navigation 和 route 对象
 * 3. 确保屏幕组件能够正确接收和使用导航参数
 * 4. 提供 TypeScript 智能提示和类型检查
 * 
 * 使用示例：
 * interface WelcomeScreenProps extends AppStackScreenProps<"Welcome"> {}
 * 
 * 包含的主要属性：
 * - navigation: 导航对象，用于页面跳转、返回等操作
 * - route: 路由对象，包含当前路由的参数和信息
 */
export interface AppStackScreenProps<ScreenName extends keyof AppStackParamList> extends NativeStackScreenProps<AppStackParamList, ScreenName> { }

// 文档：https://reactnavigation.org/docs/stack-navigator/
const Stack = createNativeStackNavigator<AppStackParamList>()

const AppStack = observer(function AppStack() {
  const {
    theme: { colors },
  } = useAppTheme()
  const { authenticationStore } = useStores()

  // 根据 hasSeenWelcome 决定初始路由
  let initialRoute: keyof AppStackParamList = "Welcome"
  if (authenticationStore.hasSeenWelcome) {
    initialRoute = "StressNavigator"
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        navigationBarColor: colors.background,
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
      initialRouteName={initialRoute}
    >
      <Stack.Screen name="Welcome" component={Screens.WelcomeScreen} />
      <Stack.Screen name="StressDayScreen" component={Screens.StressDayScreen} />
      <Stack.Screen name="MetricsDetailScreen" component={Screens.MetricsDetailScreen} />
      <Stack.Screen name="SleepDetailScreen" component={Screens.SleepDetailScreen} />
      <Stack.Screen name="FitnessDetailScreen" component={Screens.FitnessDetailScreen} />
      <Stack.Screen name="HeartRateDetailScreen" component={Screens.HeartRateDetailScreen} />
      <Stack.Screen name="StepCountDetailScreen" component={Screens.StepCountDetailScreen} />
      <Stack.Screen name="ActiveScreen" component={Screens.ActiveScreen} />
      <Stack.Screen name="DialScreen" component={Screens.DialScreen} />
      <Stack.Screen
        name="ProfileScreen"
        component={Screens.ProfileScreen as React.ComponentType<any>}
      />
      <Stack.Screen
        name="LoginScreen"
        component={Screens.LoginScreen as React.ComponentType<any>}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen name="StressNavigator" component={StressNavigator} />
    </Stack.Navigator>
  )
})

export interface NavigationProps
  extends Partial<ComponentProps<typeof NavigationContainer<AppStackParamList>>> { }

export const AppNavigator = observer(function AppNavigator(props: NavigationProps) {
  const { themeScheme, navigationTheme, setThemeContextOverride, ThemeProvider } =
    useThemeProvider()

  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return (
    <ThemeProvider value={{ themeScheme, setThemeContextOverride }}>
      <NavigationContainer ref={navigationRef} theme={navigationTheme} {...props}>
        <Screens.ErrorBoundary catchErrors={Config.catchErrors}>
          <AppStack />
        </Screens.ErrorBoundary>
      </NavigationContainer>
    </ThemeProvider>
  )
})
