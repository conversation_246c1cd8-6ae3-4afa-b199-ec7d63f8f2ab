const palette = {
  neutral100: "#FFFFFF",
  neutral200: "#F7F7F7",
  neutral300: "#E2E8F0",
  neutral400: "#CBD5E1",
  neutral500: "#94A3B8",
  neutral600: "#64748B",
  neutral700: "#475569",
  neutral800: "#334155",
  neutral900: "#0F172A",

  primary000: "#ECE9F0",
  primary100: "#BCBFD0",
  primary200: "#8489B4",
  primary301: "#699AE5",
  primary300: "#5A6B8C",
  primary400: "#3A4D6B",
  primary500: "#1B2D4D",
  primary600: "#1B225C",

  theme100: "#F7F7F7",

  secondary500: "#00BCD4",

  accent100: "#A855F7",

  success200: "#55E9BC",

  angry100: "#FEE2E2",
  angry200: "#F85F73",
  angry500: "#EF4444",
} as const

export const colors = {
  /**
   * The palette is available to use, but prefer using the name.
   * This is only included for rare, one-off cases. Try to use
   * semantic names as much as possible.
   */
  palette,
  /**
   * A helper for making something see-thru.
   */
  transparent: "rgba(0, 0, 0, 0)",
  /**
   * The default text color in many components.
   */
  text: palette.primary600,
  /**
   * Secondary text information.
   */
  textDim: palette.primary100,
  /**
   * The default color of the screen background.
   */
  background: palette.primary000,
  /**
   * The default border color.
   */
  border: palette.neutral400,
  /**
   * The main tinting color.
   */
  tint: palette.primary600,
  /**
   * The inactive tinting color.
   */
  tintInactive: palette.primary100,
  /**
   * A subtle color used for lines.
   */
  separator: palette.neutral300,
  /**
   * Error messages.
   */
  error: palette.angry500,
  /**
   * Error Background.
   */
  errorBackground: palette.angry100,
  /**
   * Positive text color.
   */
  textPositive: palette.success200,
  /**
   * Negative text color.
   */
  textNegative: palette.angry200,
} as const
